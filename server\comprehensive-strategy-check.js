/**
 * 全面的策略检查工具
 * 用于查找所有策略，特别是有持仓的策略
 */

const mongoose = require('mongoose');
require('dotenv').config();

async function comprehensiveStrategyCheck() {
  console.log('🔍 全面策略检查');
  console.log('=' .repeat(60));

  try {
    // 连接MongoDB
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/crypto-trading';
    await mongoose.connect(mongoURI, {
      serverSelectionTimeoutMS: 10000,
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('✅ 数据库连接成功\n');

    // 获取Strategy模型
    const Strategy = require('./models/Strategy');

    // 1. 查找所有OKX策略（包括已完成的）
    console.log('📊 1. 查找所有OKX策略');
    console.log('-' .repeat(40));
    
    const allOkxStrategies = await Strategy.find({
      $or: [
        { exchange: 'okx' },
        { exchange: { $exists: false } } // 兼容旧数据
      ]
    }).sort({ createdAt: -1 });

    console.log(`找到 ${allOkxStrategies.length} 个OKX策略\n`);

    // 按状态分组
    const statusGroups = {};
    allOkxStrategies.forEach(strategy => {
      if (!statusGroups[strategy.status]) {
        statusGroups[strategy.status] = [];
      }
      statusGroups[strategy.status].push(strategy);
    });

    // 显示各状态的策略数量
    console.log('策略状态分布:');
    Object.keys(statusGroups).forEach(status => {
      console.log(`  ${status}: ${statusGroups[status].length} 个策略`);
    });

    // 2. 重点检查有持仓的策略
    console.log('\n📋 2. 检查有持仓的策略');
    console.log('-' .repeat(40));

    let strategiesWithPositions = [];

    for (const strategy of allOkxStrategies) {
      if (strategy.orders && strategy.orders.length > 0) {
        const buyOrders = strategy.orders.filter(order => order.side === 'buy');
        const sellOrders = strategy.orders.filter(order => order.side === 'sell');
        
        const totalBought = buyOrders.reduce((sum, order) => sum + (order.amount || 0), 0);
        const totalSold = sellOrders.reduce((sum, order) => sum + (order.amount || 0), 0);
        const netPosition = totalBought - totalSold;

        if (netPosition > 0.00001) { // 有净持仓
          strategiesWithPositions.push({
            strategy,
            netPosition,
            totalBought,
            totalSold,
            buyOrders,
            sellOrders
          });
        }
      }
    }

    console.log(`找到 ${strategiesWithPositions.length} 个有持仓的策略:\n`);

    for (const item of strategiesWithPositions) {
      const { strategy, netPosition, totalBought, totalSold, buyOrders, sellOrders } = item;
      
      console.log(`策略: ${strategy.strategyName}`);
      console.log(`- ID: ${strategy._id}`);
      console.log(`- 用户: ${strategy.userId}`);
      console.log(`- 交易对: ${strategy.symbol}`);
      console.log(`- 类型: ${strategy.type || 'spot'}`);
      console.log(`- 状态: ${strategy.status}`);
      console.log(`- 创建时间: ${strategy.createdAt}`);
      console.log(`- 买入总量: ${totalBought.toFixed(8)}`);
      console.log(`- 卖出总量: ${totalSold.toFixed(8)}`);
      console.log(`- 净持仓: ${netPosition.toFixed(8)} ${strategy.symbol.split('-')[0]}`);
      
      // 显示订单详情
      console.log(`- 买入订单 (${buyOrders.length}个):`);
      buyOrders.forEach((order, index) => {
        console.log(`  ${index + 1}. ${order.amount} @ ${order.price || 'N/A'} (${order.orderId || 'N/A'})`);
      });
      
      console.log(`- 卖出订单 (${sellOrders.length}个):`);
      sellOrders.forEach((order, index) => {
        console.log(`  ${index + 1}. ${order.amount} @ ${order.price || 'N/A'} (${order.orderId || 'N/A'})`);
        if (order.orderId && order.orderId.includes('auto-fix')) {
          console.log(`    ⚠️ 这是自动修复订单！`);
        }
      });
      
      // 检查实际余额
      if (strategy.userId) {
        await checkUserBalance(strategy.userId, strategy.symbol);
      }
      
      console.log('');
    }

    // 3. 检查最近完成的策略
    console.log('\n📈 3. 检查最近24小时完成的策略');
    console.log('-' .repeat(40));
    
    const recentCompleted = await Strategy.find({
      $or: [
        { exchange: 'okx' },
        { exchange: { $exists: false } }
      ],
      status: 'completed',
      updatedAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    }).sort({ updatedAt: -1 });

    console.log(`找到 ${recentCompleted.length} 个最近完成的策略:\n`);

    for (const strategy of recentCompleted) {
      console.log(`策略: ${strategy.strategyName}`);
      console.log(`- ID: ${strategy._id}`);
      console.log(`- 交易对: ${strategy.symbol}`);
      console.log(`- 完成时间: ${strategy.updatedAt}`);
      
      if (strategy.orders && strategy.orders.length > 0) {
        const buyOrders = strategy.orders.filter(order => order.side === 'buy');
        const sellOrders = strategy.orders.filter(order => order.side === 'sell');
        
        console.log(`- 买入订单: ${buyOrders.length} 个`);
        console.log(`- 卖出订单: ${sellOrders.length} 个`);
        
        // 检查最后的订单
        const lastOrder = strategy.orders[strategy.orders.length - 1];
        if (lastOrder) {
          console.log(`- 最后订单: ${lastOrder.side} ${lastOrder.amount} (${lastOrder.orderId || 'N/A'})`);
          if (lastOrder.orderId && lastOrder.orderId.includes('auto-fix')) {
            console.log(`  ⚠️ 这是自动修复订单！可能存在问题`);
          }
        }
      }
      
      console.log('');
    }

    // 4. 查找现货策略1相关的策略
    console.log('\n🎯 4. 查找现货策略1（顺势抄底）相关的策略');
    console.log('-' .repeat(40));
    
    const spot1Strategies = await Strategy.find({
      $or: [
        { strategyName: { $regex: /顺势抄底/i } },
        { strategyName: { $regex: /现货策略1/i } },
        { strategyTemplate: 'spot1' },
        { strategyTemplate: 'okx_spot1' }
      ]
    }).sort({ createdAt: -1 });

    console.log(`找到 ${spot1Strategies.length} 个现货策略1相关的策略:\n`);

    for (const strategy of spot1Strategies) {
      console.log(`策略: ${strategy.strategyName}`);
      console.log(`- ID: ${strategy._id}`);
      console.log(`- 状态: ${strategy.status}`);
      console.log(`- 交易对: ${strategy.symbol}`);
      console.log(`- 创建时间: ${strategy.createdAt}`);
      
      if (strategy.orders && strategy.orders.length > 0) {
        const buyOrders = strategy.orders.filter(order => order.side === 'buy');
        const sellOrders = strategy.orders.filter(order => order.side === 'sell');
        const totalBought = buyOrders.reduce((sum, order) => sum + (order.amount || 0), 0);
        const totalSold = sellOrders.reduce((sum, order) => sum + (order.amount || 0), 0);
        const netPosition = totalBought - totalSold;
        
        console.log(`- 净持仓: ${netPosition.toFixed(8)} ${strategy.symbol.split('-')[0]}`);
        
        if (netPosition > 0.00001) {
          console.log(`  ⚠️ 这个策略有持仓！`);
        }
      }
      
      console.log('');
    }

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
  }
}

/**
 * 检查用户实际余额
 */
async function checkUserBalance(userId, symbol) {
  try {
    console.log(`\n🔍 检查用户 ${userId} 的实际余额...`);
    
    const okxService = require('./services/okxService');
    const client = okxService.getOkxClient(userId);
    
    if (!client) {
      console.log('❌ 无法创建OKX客户端，API密钥未设置');
      return;
    }

    // 获取账户余额
    const balanceResponse = await client.getBalance();
    
    if (balanceResponse && balanceResponse.data && Array.isArray(balanceResponse.data)) {
      const baseCurrency = symbol.split('-')[0];
      
      for (const balanceData of balanceResponse.data) {
        if (balanceData.details && Array.isArray(balanceData.details)) {
          const currencyBalance = balanceData.details.find(detail => detail.ccy === baseCurrency);
          if (currencyBalance) {
            console.log(`💰 ${baseCurrency} 实际余额:`);
            console.log(`  可用: ${currencyBalance.availBal}`);
            console.log(`  冻结: ${currencyBalance.frozenBal || 0}`);
            console.log(`  总计: ${currencyBalance.bal}`);
            
            const availBal = parseFloat(currencyBalance.availBal);
            if (availBal <= 0) {
              console.log(`⚠️ ${baseCurrency} 可用余额为0，无法平仓`);
            } else {
              console.log(`✅ ${baseCurrency} 有可用余额，可以平仓`);
            }
          } else {
            console.log(`❌ 未找到 ${baseCurrency} 余额`);
          }
        }
      }
    } else {
      console.log('❌ 获取余额响应格式异常');
    }
  } catch (error) {
    console.error(`❌ 检查用户余额失败: ${error.message}`);
  }
}

// 运行检查
if (require.main === module) {
  comprehensiveStrategyCheck().catch(console.error);
}

module.exports = { comprehensiveStrategyCheck, checkUserBalance };
