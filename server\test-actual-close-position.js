const axios = require('axios');

async function testActualClosePosition() {
  try {
    console.log('🧪 测试实际的一键平仓API调用...');
    
    // 策略ID（从之前的测试中获得）
    const strategyId = '684b74121a5e8072b729761b';
    
    console.log(`📋 测试策略ID: ${strategyId}`);
    console.log(`🎯 策略名称: 现货策略1（顺势抄底）`);
    
    // 调用平仓API
    console.log('\n🚀 发送平仓请求...');
    
    try {
      const response = await axios.post(`http://localhost:3009/api/strategies/${strategyId}/close-position`, {}, {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      console.log('\n📊 API响应:');
      console.log('状态码:', response.status);
      console.log('响应数据:', JSON.stringify(response.data, null, 2));
      
      if (response.data.success) {
        console.log('\n✅ 前端会显示: 平仓成功！');
        
        if (response.data.autoFixed) {
          console.log('🔧 检测到自动修复情况');
        }
      } else {
        console.log('\n❌ 前端会显示: 平仓失败');
        console.log('错误信息:', response.data.error);
      }
      
    } catch (apiError) {
      console.log('\n❌ API调用失败:');
      console.log('错误状态:', apiError.response?.status);
      console.log('错误数据:', JSON.stringify(apiError.response?.data, null, 2));
      console.log('错误信息:', apiError.message);
      
      console.log('\n📋 这就是为什么前端显示平仓成功但实际未平仓的原因！');
    }
    
    // 检查策略状态是否改变
    console.log('\n🔍 检查策略状态变化...');
    
    try {
      const statusResponse = await axios.get('http://localhost:3009/api/test-strategies?userId=default');
      
      if (statusResponse.data.success && statusResponse.data.strategies) {
        const strategy = statusResponse.data.strategies.find(s => 
          (s.id === strategyId || s._id === strategyId)
        );
        
        if (strategy) {
          console.log(`策略当前状态: ${strategy.status}`);
          console.log(`策略名称: ${strategy.strategyName}`);
          
          if (strategy.status === 'completed') {
            console.log('✅ 策略状态已更新为completed');
          } else if (strategy.status === 'active') {
            console.log('⚠️ 策略状态仍为active，说明平仓可能失败了');
          }
        } else {
          console.log('❌ 找不到对应的策略');
        }
      }
    } catch (statusError) {
      console.error('❌ 检查策略状态失败:', statusError.message);
    }
    
    console.log('\n📋 总结:');
    console.log('1. 如果API返回success: true，前端就会显示"平仓成功"');
    console.log('2. 但实际的平仓操作可能在executeExit()中失败');
    console.log('3. 需要检查服务器日志来确认真实的执行结果');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testActualClosePosition();
