/**
 * 修复BTC持仓问题
 * 针对策略ID: 684b74121a5e8072b729761b 的BTC平仓问题
 */

const mongoose = require('mongoose');
require('dotenv').config();

async function fixBTCPosition() {
  console.log('🔧 修复BTC持仓问题');
  console.log('=' .repeat(50));

  try {
    // 连接MongoDB
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/crypto-trading';
    await mongoose.connect(mongoURI, {
      serverSelectionTimeoutMS: 10000
    });
    console.log('✅ 数据库连接成功\n');

    // 获取Strategy模型
    const Strategy = require('./models/Strategy');
    
    // 1. 检查问题策略
    console.log('📋 1. 检查问题策略');
    console.log('-' .repeat(30));
    
    const problemStrategyId = '684b74121a5e8072b729761b';
    const strategy = await Strategy.findById(problemStrategyId);
    
    if (!strategy) {
      console.log('❌ 未找到问题策略');
      return;
    }
    
    console.log(`策略: ${strategy.strategyName}`);
    console.log(`交易对: ${strategy.symbol}`);
    console.log(`状态: ${strategy.status}`);
    console.log(`用户: ${strategy.userId}`);
    
    // 检查订单历史
    if (strategy.orders && strategy.orders.length > 0) {
      console.log(`\n订单历史 (${strategy.orders.length}个):`);
      strategy.orders.forEach((order, index) => {
        console.log(`${index + 1}. ${order.side} ${order.amount} @ ${order.price || 'N/A'} (${order.orderId || 'N/A'})`);
        if (order.orderId && order.orderId.includes('auto-fix')) {
          console.log(`   ⚠️ 自动修复订单 - 这是虚假记录！`);
        }
      });
      
      const buyOrders = strategy.orders.filter(order => order.side === 'buy');
      const sellOrders = strategy.orders.filter(order => order.side === 'sell');
      const totalBought = buyOrders.reduce((sum, order) => sum + (order.amount || 0), 0);
      const totalSold = sellOrders.reduce((sum, order) => sum + (order.amount || 0), 0);
      
      console.log(`\n计算结果:`);
      console.log(`- 买入总量: ${totalBought.toFixed(8)} BTC`);
      console.log(`- 卖出总量: ${totalSold.toFixed(8)} BTC`);
      console.log(`- 净持仓: ${(totalBought - totalSold).toFixed(8)} BTC`);
    }
    
    // 2. 检查实际BTC余额
    console.log('\n💰 2. 检查实际BTC余额');
    console.log('-' .repeat(30));
    
    const userId = strategy.userId || 'default';
    const actualBTCBalance = await getActualBTCBalance(userId);
    
    if (actualBTCBalance === null) {
      console.log('❌ 无法获取实际BTC余额，请检查API配置');
      return;
    }
    
    console.log(`实际BTC余额: ${actualBTCBalance} BTC`);
    
    // 3. 执行真正的BTC平仓
    if (actualBTCBalance > 0.00001) { // 如果有BTC余额
      console.log('\n🚀 3. 执行真正的BTC平仓');
      console.log('-' .repeat(30));
      
      const sellResult = await executeBTCSell(userId, actualBTCBalance);
      
      if (sellResult.success) {
        console.log('✅ BTC平仓成功！');
        console.log(`卖出数量: ${sellResult.quantity} BTC`);
        console.log(`订单ID: ${sellResult.orderId}`);
        
        // 更新策略记录，添加真实的卖出订单
        if (sellResult.orderId) {
          strategy.orders.push({
            orderId: sellResult.orderId,
            side: 'sell',
            type: 'market',
            amount: sellResult.quantity,
            price: sellResult.price || 0,
            status: 'filled',
            timestamp: new Date()
          });
          
          await strategy.save();
          console.log('✅ 策略记录已更新');
        }
        
        // 添加日志
        if (strategy.addLog) {
          await strategy.addLog(`手动修复: 执行真实BTC平仓 ${sellResult.quantity} BTC`);
        }
        
      } else {
        console.log('❌ BTC平仓失败:', sellResult.error);
      }
    } else {
      console.log('✅ 实际BTC余额为0，无需平仓');
      console.log('💡 问题已解决：自动修复记录是正确的');
    }
    
    // 4. 验证最终状态
    console.log('\n🔍 4. 验证最终状态');
    console.log('-' .repeat(30));
    
    const finalBalance = await getActualBTCBalance(userId);
    console.log(`最终BTC余额: ${finalBalance || 0} BTC`);
    
    if (finalBalance <= 0.00001) {
      console.log('✅ 修复完成！BTC已成功平仓');
    } else {
      console.log('⚠️ BTC仍有余额，可能需要手动处理');
    }

  } catch (error) {
    console.error('❌ 修复失败:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
  }
}

/**
 * 获取实际BTC余额
 */
async function getActualBTCBalance(userId) {
  try {
    console.log(`🔍 获取用户 ${userId} 的实际BTC余额...`);
    
    const okxService = require('./services/okxService');
    const client = okxService.getOkxClient(userId);
    
    if (!client) {
      console.log('❌ 无法创建OKX客户端，API密钥未设置');
      return null;
    }

    // 获取账户余额
    const balanceResponse = await client.getBalance();
    
    if (balanceResponse && balanceResponse.data && Array.isArray(balanceResponse.data)) {
      for (const balanceData of balanceResponse.data) {
        if (balanceData.details && Array.isArray(balanceData.details)) {
          const btcBalance = balanceData.details.find(detail => detail.ccy === 'BTC');
          if (btcBalance) {
            const availBal = parseFloat(btcBalance.availBal);
            console.log(`💰 BTC余额详情:`);
            console.log(`  可用: ${btcBalance.availBal}`);
            console.log(`  冻结: ${btcBalance.frozenBal || 0}`);
            console.log(`  总计: ${btcBalance.bal}`);
            return availBal;
          }
        }
      }
    }
    
    console.log('❌ 未找到BTC余额');
    return 0;
  } catch (error) {
    console.error(`❌ 获取BTC余额失败: ${error.message}`);
    return null;
  }
}

/**
 * 执行BTC卖出
 */
async function executeBTCSell(userId, btcAmount) {
  try {
    console.log(`🚀 准备卖出 ${btcAmount} BTC...`);
    
    const okxService = require('./services/okxService');
    const client = okxService.getOkxClient(userId);
    
    if (!client) {
      return { success: false, error: 'OKX客户端创建失败' };
    }
    
    // 计算安全的卖出数量（99%，避免精度问题）
    const sellQuantity = Math.floor(btcAmount * 0.99 * 100000000) / 100000000; // 8位小数
    
    console.log(`计算卖出数量: ${sellQuantity} BTC (原始: ${btcAmount})`);
    
    if (sellQuantity <= 0) {
      return { success: false, error: '卖出数量太小' };
    }
    
    // 执行市价卖出
    const sellOrder = await client.submitOrder({
      instId: 'BTC-USDT',
      tdMode: 'cash',
      side: 'sell',
      ordType: 'market',
      sz: String(sellQuantity)
    });
    
    console.log('OKX卖出订单响应:', sellOrder);
    
    if (sellOrder && sellOrder.data && sellOrder.data[0]) {
      const orderData = sellOrder.data[0];
      return {
        success: true,
        quantity: sellQuantity,
        orderId: orderData.ordId,
        price: orderData.px || 0
      };
    } else {
      return { success: false, error: '订单响应格式异常' };
    }
    
  } catch (error) {
    console.error('执行BTC卖出失败:', error);
    return { success: false, error: error.message };
  }
}

// 运行修复
if (require.main === module) {
  fixBTCPosition().catch(console.error);
}

module.exports = { fixBTCPosition, getActualBTCBalance, executeBTCSell };
