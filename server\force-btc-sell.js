/**
 * 强制BTC平仓脚本
 * 直接卖出账户中的所有BTC
 */

const mongoose = require('mongoose');
require('dotenv').config();

async function forceBTCSell() {
  console.log('🚀 强制BTC平仓');
  console.log('=' .repeat(50));

  try {
    // 连接MongoDB
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/crypto-trading';
    await mongoose.connect(mongoURI, {
      serverSelectionTimeoutMS: 10000
    });
    console.log('✅ 数据库连接成功\n');

    // 1. 使用Python服务获取准确的BTC余额
    console.log('💰 1. 获取BTC余额');
    console.log('-' .repeat(30));
    
    const pythonTradeService = require('./services/pythonTradeService');
    const balanceResult = await pythonTradeService.getOkxBalance('default');
    
    console.log('余额查询结果:', JSON.stringify(balanceResult, null, 2));
    
    if (!balanceResult.success) {
      console.log('❌ 获取余额失败:', balanceResult.error);
      return;
    }
    
    const btcBalance = balanceResult.balance?.BTC;
    if (!btcBalance) {
      console.log('❌ 未找到BTC余额');
      return;
    }
    
    const availableBTC = parseFloat(btcBalance.free);
    console.log(`💰 BTC余额: ${availableBTC} BTC`);
    console.log(`💰 BTC总计: ${btcBalance.total} BTC`);
    console.log(`💰 BTC冻结: ${btcBalance.used} BTC`);
    
    if (availableBTC <= 0.00001) {
      console.log('✅ BTC余额太少，无需平仓');
      return;
    }
    
    // 2. 执行BTC卖出
    console.log('\n🚀 2. 执行BTC卖出');
    console.log('-' .repeat(30));
    
    // 计算安全的卖出数量（99%，避免精度问题）
    const sellQuantity = Math.floor(availableBTC * 0.99 * 100000000) / 100000000; // 8位小数
    
    console.log(`计算卖出数量: ${sellQuantity} BTC`);
    console.log(`原始余额: ${availableBTC} BTC`);
    console.log(`安全系数: 99%`);
    
    if (sellQuantity <= 0) {
      console.log('❌ 计算后的卖出数量为0');
      return;
    }
    
    // 确认操作
    console.log('\n⚠️ 即将执行BTC卖出操作');
    console.log(`卖出数量: ${sellQuantity} BTC`);
    console.log(`交易对: BTC-USDT`);
    console.log(`订单类型: 市价单`);
    
    // 这里可以添加确认机制
    // const readline = require('readline');
    // const rl = readline.createInterface({
    //   input: process.stdin,
    //   output: process.stdout
    // });
    // 
    // const answer = await new Promise(resolve => {
    //   rl.question('确认执行卖出操作？(y/N): ', resolve);
    // });
    // rl.close();
    // 
    // if (answer.toLowerCase() !== 'y') {
    //   console.log('❌ 操作已取消');
    //   return;
    // }
    
    // 执行卖出
    const sellResult = await executeBTCSellDirect('default', sellQuantity);
    
    if (sellResult.success) {
      console.log('✅ BTC卖出成功！');
      console.log(`卖出数量: ${sellResult.quantity} BTC`);
      console.log(`订单ID: ${sellResult.orderId}`);
      console.log(`成交价格: ${sellResult.price || 'N/A'}`);
      
      // 3. 更新策略记录
      console.log('\n📝 3. 更新策略记录');
      console.log('-' .repeat(30));
      
      const Strategy = require('./models/Strategy');
      const problemStrategyId = '684b74121a5e8072b729761b';
      const strategy = await Strategy.findById(problemStrategyId);
      
      if (strategy) {
        // 移除自动修复的虚假订单
        const autoFixIndex = strategy.orders.findIndex(order => 
          order.orderId && order.orderId.includes('auto-fix')
        );
        
        if (autoFixIndex !== -1) {
          console.log('🗑️ 移除自动修复的虚假订单');
          strategy.orders.splice(autoFixIndex, 1);
        }
        
        // 添加真实的卖出订单
        strategy.orders.push({
          orderId: sellResult.orderId,
          side: 'sell',
          type: 'market',
          amount: sellResult.quantity,
          price: sellResult.price || 0,
          status: 'filled',
          timestamp: new Date()
        });
        
        await strategy.save();
        console.log('✅ 策略记录已更新');
        
        // 添加日志
        if (strategy.addLog) {
          await strategy.addLog(`手动修复: 执行真实BTC平仓 ${sellResult.quantity} BTC，订单ID: ${sellResult.orderId}`);
        }
      }
      
      // 4. 验证最终状态
      console.log('\n🔍 4. 验证最终状态');
      console.log('-' .repeat(30));
      
      // 等待一下让订单处理完成
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const finalBalanceResult = await pythonTradeService.getOkxBalance('default');
      if (finalBalanceResult.success) {
        const finalBTC = finalBalanceResult.balance?.BTC;
        if (finalBTC) {
          console.log(`最终BTC余额: ${finalBTC.free} BTC`);
          if (parseFloat(finalBTC.free) <= 0.00001) {
            console.log('✅ 修复完成！BTC已成功平仓');
          } else {
            console.log('⚠️ BTC仍有余额，可能需要再次处理');
          }
        } else {
          console.log('✅ 修复完成！BTC余额已清零');
        }
      }
      
    } else {
      console.log('❌ BTC卖出失败:', sellResult.error);
    }

  } catch (error) {
    console.error('❌ 操作失败:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
  }
}

/**
 * 直接执行BTC卖出
 */
async function executeBTCSellDirect(userId, btcAmount) {
  try {
    console.log(`🚀 准备卖出 ${btcAmount} BTC...`);
    
    const okxService = require('./services/okxService');
    const client = okxService.getOkxClient(userId);
    
    if (!client) {
      return { success: false, error: 'OKX客户端创建失败' };
    }
    
    console.log(`执行市价卖出订单...`);
    console.log(`交易对: BTC-USDT`);
    console.log(`数量: ${btcAmount} BTC`);
    
    // 执行市价卖出
    const sellOrder = await client.submitOrder({
      instId: 'BTC-USDT',
      tdMode: 'cash',
      side: 'sell',
      ordType: 'market',
      sz: String(btcAmount)
    });
    
    console.log('OKX卖出订单响应:', JSON.stringify(sellOrder, null, 2));
    
    if (sellOrder && sellOrder.data && sellOrder.data[0]) {
      const orderData = sellOrder.data[0];
      
      // 检查订单状态
      if (orderData.sCode === '0') {
        return {
          success: true,
          quantity: btcAmount,
          orderId: orderData.ordId,
          price: orderData.px || 0
        };
      } else {
        return { 
          success: false, 
          error: `订单失败: ${orderData.sMsg} (${orderData.sCode})` 
        };
      }
    } else {
      return { success: false, error: '订单响应格式异常' };
    }
    
  } catch (error) {
    console.error('执行BTC卖出失败:', error);
    return { success: false, error: error.message };
  }
}

// 运行强制平仓
if (require.main === module) {
  forceBTCSell().catch(console.error);
}

module.exports = { forceBTCSell, executeBTCSellDirect };
