/**
 * 验证BTC状态脚本
 * 检查订单状态和当前余额
 */

const mongoose = require('mongoose');
require('dotenv').config();

async function verifyBTCStatus() {
  console.log('🔍 验证BTC状态');
  console.log('=' .repeat(50));

  try {
    // 连接MongoDB
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/crypto-trading';
    await mongoose.connect(mongoURI, {
      serverSelectionTimeoutMS: 10000
    });
    console.log('✅ 数据库连接成功\n');

    const okxService = require('./services/okxService');
    const userId = 'default';
    const client = okxService.getOkxClient(userId);
    
    if (!client) {
      console.log('❌ 无法创建OKX客户端');
      return;
    }

    // 1. 检查当前BTC余额
    console.log('💰 1. 检查当前BTC余额');
    console.log('-' .repeat(30));
    
    const balanceResponse = await client.getBalance();
    let btcBalance = null;
    
    if (Array.isArray(balanceResponse)) {
      for (const balanceData of balanceResponse) {
        if (balanceData.details && Array.isArray(balanceData.details)) {
          btcBalance = balanceData.details.find(detail => detail.ccy === 'BTC');
          if (btcBalance) break;
        }
      }
    }
    
    if (btcBalance) {
      console.log(`💰 当前BTC余额: ${btcBalance.availBal} BTC`);
      console.log(`💰 BTC总余额: ${btcBalance.eq} BTC`);
      console.log(`💰 BTC冻结: ${btcBalance.frozenBal || 0} BTC`);
      
      const currentBalance = parseFloat(btcBalance.availBal);
      if (currentBalance <= 0.00001) {
        console.log('✅ BTC余额已清零，平仓成功！');
      } else {
        console.log('⚠️ BTC仍有余额');
      }
    } else {
      console.log('✅ 未找到BTC余额，说明已清零！');
    }

    // 2. 检查最近的订单历史
    console.log('\n📋 2. 检查最近的订单历史');
    console.log('-' .repeat(30));
    
    try {
      // 获取最近的订单历史
      const ordersResponse = await client.getOrderHistory({
        instType: 'SPOT',
        instId: 'BTC-USDT',
        limit: '10'
      });
      
      console.log('订单历史响应:', JSON.stringify(ordersResponse, null, 2));
      
      let orders = [];
      if (Array.isArray(ordersResponse)) {
        orders = ordersResponse;
      } else if (ordersResponse && ordersResponse.data) {
        orders = ordersResponse.data;
      }
      
      if (orders && orders.length > 0) {
        console.log(`找到 ${orders.length} 个最近订单:`);
        
        orders.forEach((order, index) => {
          console.log(`\n订单 ${index + 1}:`);
          console.log(`- 订单ID: ${order.ordId}`);
          console.log(`- 方向: ${order.side}`);
          console.log(`- 数量: ${order.sz}`);
          console.log(`- 价格: ${order.px || order.avgPx || 'N/A'}`);
          console.log(`- 状态: ${order.state}`);
          console.log(`- 时间: ${new Date(parseInt(order.cTime)).toLocaleString()}`);
          
          // 检查是否是刚才的卖出订单
          if (order.ordId === '2640143998122942464') {
            console.log(`🎯 这是刚才提交的BTC卖出订单！`);
            if (order.state === 'filled') {
              console.log(`✅ 订单已成交！`);
            } else if (order.state === 'live') {
              console.log(`⏳ 订单还在处理中...`);
            } else {
              console.log(`❓ 订单状态: ${order.state}`);
            }
          }
        });
      } else {
        console.log('未找到最近的订单');
      }
      
    } catch (orderError) {
      console.log('获取订单历史失败:', orderError.message);
    }

    // 3. 检查特定订单状态
    console.log('\n🎯 3. 检查特定订单状态');
    console.log('-' .repeat(30));
    
    const targetOrderId = '2640143998122942464';
    try {
      const orderResponse = await client.getOrderDetails({
        instId: 'BTC-USDT',
        ordId: targetOrderId
      });
      
      console.log('订单详情响应:', JSON.stringify(orderResponse, null, 2));
      
      let orderDetails = null;
      if (Array.isArray(orderResponse) && orderResponse[0]) {
        orderDetails = orderResponse[0];
      } else if (orderResponse && orderResponse.data && orderResponse.data[0]) {
        orderDetails = orderResponse.data[0];
      }
      
      if (orderDetails) {
        console.log(`📋 订单 ${targetOrderId} 详情:`);
        console.log(`- 状态: ${orderDetails.state}`);
        console.log(`- 方向: ${orderDetails.side}`);
        console.log(`- 数量: ${orderDetails.sz}`);
        console.log(`- 成交数量: ${orderDetails.accFillSz || 0}`);
        console.log(`- 平均价格: ${orderDetails.avgPx || 'N/A'}`);
        console.log(`- 手续费: ${orderDetails.fee || 'N/A'}`);
        
        if (orderDetails.state === 'filled') {
          console.log('✅ 订单已完全成交！BTC平仓成功！');
        } else if (orderDetails.state === 'live') {
          console.log('⏳ 订单还在执行中...');
        } else if (orderDetails.state === 'canceled') {
          console.log('❌ 订单已取消');
        } else {
          console.log(`❓ 订单状态: ${orderDetails.state}`);
        }
      } else {
        console.log('未找到订单详情');
      }
      
    } catch (detailError) {
      console.log('获取订单详情失败:', detailError.message);
    }

    // 4. 更新策略记录（如果订单成功）
    console.log('\n📝 4. 检查是否需要更新策略记录');
    console.log('-' .repeat(30));
    
    const Strategy = require('./models/Strategy');
    const problemStrategyId = '684b74121a5e8072b729761b';
    const strategy = await Strategy.findById(problemStrategyId);
    
    if (strategy) {
      console.log(`策略: ${strategy.strategyName}`);
      console.log(`当前订单数: ${strategy.orders ? strategy.orders.length : 0}`);
      
      // 检查是否已经有这个订单记录
      const hasTargetOrder = strategy.orders && strategy.orders.some(order => 
        order.orderId === targetOrderId
      );
      
      if (hasTargetOrder) {
        console.log('✅ 策略记录中已包含此订单');
      } else {
        console.log('⚠️ 策略记录中未找到此订单，可能需要手动添加');
      }
      
      // 检查是否还有自动修复订单
      const autoFixOrder = strategy.orders && strategy.orders.find(order => 
        order.orderId && order.orderId.includes('auto-fix')
      );
      
      if (autoFixOrder) {
        console.log('⚠️ 仍有自动修复的虚假订单需要清理');
      } else {
        console.log('✅ 没有虚假的自动修复订单');
      }
    }

  } catch (error) {
    console.error('❌ 验证失败:', error.message);
  } finally {
    await mongoose.disconnect();
  }
}

// 运行验证
if (require.main === module) {
  verifyBTCStatus().catch(console.error);
}

module.exports = { verifyBTCStatus };
