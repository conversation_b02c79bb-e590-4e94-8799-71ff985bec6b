<template>
  <div class="okx-view">
    <!-- 市场头部 -->
    <div class="market-header">
      <h2>OKX 自动交易平台</h2>
      <p>专业的加密货币自动交易策略管理</p>
    </div>

    <div class="main-content">
      <!-- API连接状态 -->
      <div class="api-status-section" v-if="apiConnected">
        <div class="api-status-info">
          <div class="api-status-text">
            <i class="fas fa-check-circle"></i>
            OKX API 已连接
          </div>
          <div class="api-actions">
            <button class="reconfigure-button" @click="reconfigureApi">
              <i class="fas fa-cog"></i>
              重新配置
            </button>
            <button 
              class="delete-api-button" 
              @click="showDeleteApiConfirm = true"
              :disabled="isDeletingApi"
            >
              <i class="fas fa-trash"></i>
              删除API
            </button>
          </div>
        </div>
      </div>

      <!-- 未连接API时的提示 -->
      <div class="connect-api-section" v-else>
        <div class="card">
          <div class="card-content">
            <h3>连接OKX API</h3>
            <p>请先配置OKX API密钥以使用自动交易功能</p>
            <button class="connect-button" @click="showApiModal = true">
              <i class="fas fa-plug"></i>
              配置API密钥
            </button>
          </div>
        </div>
      </div>

      <!-- 账户信息面板 -->
      <AccountInfoPanel 
        v-if="apiConnected"
        :userId="userId"
        class="account-info-panel"
      />

      <!-- 策略创建表单 -->
      <div class="strategy-form" v-if="apiConnected">
        <OkxStrategyForm 
          :userId="userId"
          @strategy-created="fetchStrategies"
        />
      </div>

      <!-- 策略列表 -->
      <div class="active-strategies" v-if="apiConnected">
        <div class="section-header">
          <h3 class="section-title">策略管理</h3>
          <div class="view-controls">
            <button 
              class="view-btn" 
              :class="{ active: viewMode === 'card' }"
              @click="viewMode = 'card'"
            >
              <i class="fas fa-th-large"></i>
              卡片视图
            </button>
            <button 
              class="view-btn" 
              :class="{ active: viewMode === 'compact' }"
              @click="viewMode = 'compact'"
            >
              <i class="fas fa-list"></i>
              列表视图
            </button>
          </div>
        </div>

        <!-- 筛选和搜索 -->
        <div class="filter-controls">
          <div class="filter-group">
            <select v-model="statusFilter" class="filter-select">
              <option value="all">全部状态</option>
              <option value="running">运行中</option>
              <option value="completed">已完成</option>
            </select>
            <input 
              v-model="searchQuery"
              type="text"
              placeholder="搜索策略名称、交易对..."
              class="search-input"
            >
          </div>
          
          <!-- 批量操作 -->
          <div class="batch-actions" v-if="filteredStrategies.length > 0">
            <label class="select-all-checkbox">
              <input 
                type="checkbox"
                :checked="isAllSelected"
                :indeterminate="isPartiallySelected"
                @change="toggleSelectAll"
              >
              <span class="checkmark"></span>
              全选
            </label>
            
            <button 
              v-if="hasRunningSelectedStrategies"
              class="batch-btn stop-btn"
              @click="showBatchStopConfirm = true"
            >
              <i class="fas fa-stop"></i>
              批量停止 ({{ runningSelectedCount }})
            </button>
            
            <button 
              v-if="hasNonRunningSelectedStrategies"
              class="batch-btn delete-btn"
              @click="showBatchDeleteConfirm = true"
            >
              <i class="fas fa-trash"></i>
              批量删除 ({{ nonRunningSelectedCount }})
            </button>
          </div>
        </div>

        <!-- 策略统计 -->
        <div class="strategy-stats">
          <div class="stat-item">
            <span class="stat-label">运行中:</span>
            <span class="stat-value running">{{ runningStrategiesCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">已完成:</span>
            <span class="stat-value completed">{{ completedStrategiesCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">总计:</span>
            <span class="stat-value total">{{ strategies.length }}</span>
          </div>
        </div>

        <!-- 策略列表内容 -->
        <div class="strategies-content">
          <!-- 无策略提示 -->
          <div v-if="filteredStrategies.length === 0" class="no-strategies">
            <i class="fas fa-inbox"></i>
            <p>{{ getNoStrategiesText() }}</p>
          </div>

          <!-- 卡片视图 -->
          <div v-else-if="viewMode === 'card'" class="strategies-list">
            <div
              v-for="(strategy, index) in filteredStrategies"
              :key="index"
              class="strategy-card"
              :class="{ 'running-strategy': isRunningStrategy(strategy) }"
            >
              <div class="strategy-header">
                <div class="strategy-info">
                  <h4 class="strategy-name">{{ strategy.strategyName || '未命名策略' }}</h4>
                  <div class="strategy-meta">
                    <span class="strategy-type">{{ strategy.type === 'spot' ? '现货' : '合约' }}</span>
                    <span class="strategy-symbol">{{ strategy.symbol }}</span>
                  </div>
                </div>
                <div class="strategy-status" :class="getStatusClass(strategy.status)">
                  {{ getStatusText(strategy.status) }}
                </div>
              </div>

              <div class="strategy-body">
                <div class="strategy-details">
                  <div class="detail-item">
                    <span class="detail-label">投资金额:</span>
                    <span class="detail-value">{{ strategy.amount }} USDT</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">创建时间:</span>
                    <span class="detail-value">{{ formatDate(strategy.createdAt) }}</span>
                  </div>
                  <div class="detail-item" v-if="strategy.profit !== undefined">
                    <span class="detail-label">当前盈利:</span>
                    <span 
                      class="detail-value"
                      :class="getProfitClass(strategy)"
                    >
                      {{ calculateProfitAmount(strategy) }} USDT
                      ({{ calculateProfitPercentage(strategy) }}%)
                    </span>
                  </div>
                </div>

                <div class="strategy-actions">
                  <template v-if="strategy.status === 'waiting' || strategy.status === 'active'">
                    <button
                      class="stop-button"
                      @click="stopStrategy(strategy.id || strategy._id)"
                    >
                      停止策略
                    </button>
                    <button
                      v-if="strategy.status === 'active' && hasPosition(strategy)"
                      class="close-position-button"
                      @click="closePosition(strategy.id || strategy._id)"
                      title="立即平仓所有持仓"
                    >
                      <i class="fas fa-hand-paper"></i>
                      一键平仓
                    </button>
                  </template>
                  <div v-else-if="strategy.status !== 'error'" class="strategy-status-info">
                    {{ getStatusText(strategy.status) }}
                  </div>
                  <template v-if="strategy.status === 'error'">
                    <button
                      class="recover-button"
                      @click="recoverStrategy(strategy.id || strategy._id)"
                      title="恢复策略监控，继续执行交易"
                    >
                      <i class="fas fa-redo"></i>
                      恢复策略
                    </button>
                    <button
                      class="delete-button"
                      @click="deleteStrategy(strategy.id || strategy._id)"
                      title="永久删除策略"
                    >
                      <i class="fas fa-trash"></i>
                      删除
                    </button>
                  </template>
                </div>

                <div class="delete-action">
                  <button
                    v-if="strategy.status !== 'waiting' && strategy.status !== 'active'"
                    class="delete-button-small"
                    title="删除策略"
                    @click="deleteStrategy(strategy.id || strategy._id)"
                  >
                    <span class="delete-icon">×</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 紧凑视图 -->
          <div v-else-if="viewMode === 'compact'" class="strategies-compact-list">
            <div class="compact-header">
              <div class="compact-col checkbox-col">选择</div>
              <div class="compact-col strategy-name-col">策略名称</div>
              <div class="compact-col">交易对</div>
              <div class="compact-col">状态</div>
              <div class="compact-col">金额</div>
              <div class="compact-col">盈利</div>
              <div class="compact-col">操作</div>
            </div>
            <div
              v-for="(strategy, index) in filteredStrategies"
              :key="index"
              class="compact-strategy-row"
              :class="{ 'running-strategy': isRunningStrategy(strategy) }"
            >
              <div class="compact-col checkbox-col">
                <label class="strategy-checkbox-compact">
                  <input
                    type="checkbox"
                    :value="strategy.id || strategy._id"
                    v-model="selectedStrategies"
                  >
                  <span class="checkmark-small"></span>
                </label>
              </div>
              <div class="compact-col strategy-name-col">
                <div class="strategy-name-compact">{{ strategy.strategyName || '未命名策略' }}</div>
                <div class="strategy-type-compact">{{ strategy.type === 'spot' ? '现货' : '合约' }}</div>
              </div>
              <div class="compact-col">
                <span class="symbol-text">{{ strategy.symbol }}</span>
              </div>
              <div class="compact-col">
                <span class="strategy-status" :class="getStatusClass(strategy.status)">
                  {{ getStatusText(strategy.status) }}
                </span>
              </div>
              <div class="compact-col">
                <span class="amount-text">{{ strategy.amount }} USDT</span>
              </div>
              <div class="compact-col">
                <span
                  v-if="strategy.status === 'active' || (strategy.profit !== undefined && strategy.profit !== 0)"
                  :class="getProfitClass(strategy)"
                  class="profit-text"
                >
                  {{ calculateProfitAmount(strategy) }} USDT
                </span>
                <span v-else class="profit-text">-</span>
              </div>
              <div class="compact-col">
                <div class="compact-actions">
                  <button
                    v-if="strategy.status === 'waiting' || strategy.status === 'active'"
                    class="compact-btn stop-btn"
                    @click="stopStrategy(strategy.id || strategy._id)"
                    title="停止策略"
                  >
                    <i class="fas fa-stop"></i>
                  </button>
                  <button
                    v-if="strategy.status === 'active' && hasPosition(strategy)"
                    class="compact-btn close-btn"
                    @click="closePosition(strategy.id || strategy._id)"
                    title="一键平仓"
                  >
                    <i class="fas fa-hand-paper"></i>
                  </button>
                  <button
                    v-if="strategy.status === 'error'"
                    class="compact-btn recover-btn"
                    @click="recoverStrategy(strategy.id || strategy._id)"
                    title="恢复策略"
                  >
                    <i class="fas fa-redo"></i>
                  </button>
                  <button
                    v-if="strategy.status !== 'waiting' && strategy.status !== 'active'"
                    class="compact-btn delete-btn"
                    @click="deleteStrategy(strategy.id || strategy._id)"
                    title="删除策略"
                  >
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 历史订单组件 -->
        <div id="order-history-section" v-if="showOrderHistory">
          <OkxOrderHistory
            :apiConnected="apiConnected"
            :userId="userId"
            :visible="showOrderHistory"
          />
        </div>
      </div>
    </div>

    <!-- 浮动操作按钮 -->
    <div class="floating-actions" v-if="apiConnected">
      <div class="floating-btn-group">
        <!-- 回到顶部按钮 -->
        <button
          class="floating-btn scroll-top-btn"
          @click="scrollToTop"
          v-show="showScrollTop"
          title="回到顶部"
        >
          <i class="fas fa-arrow-up"></i>
        </button>

        <!-- 快速导航按钮 -->
        <button
          class="floating-btn nav-btn"
          @click="showQuickNav = !showQuickNav"
          title="快速导航"
        >
          <i class="fas fa-compass"></i>
        </button>
      </div>

      <!-- 快速导航菜单 -->
      <div class="quick-nav-menu" v-show="showQuickNav">
        <button class="nav-item" @click="scrollToSection('account-info')">
          <i class="fas fa-wallet"></i>
          <span>账户信息</span>
        </button>
        <button class="nav-item" @click="scrollToSection('strategy-form')">
          <i class="fas fa-plus"></i>
          <span>创建策略</span>
        </button>
        <button class="nav-item" @click="scrollToSection('strategies-list')">
          <i class="fas fa-list"></i>
          <span>策略列表</span>
        </button>
        <button class="nav-item" @click="toggleOrderHistory">
          <i class="fas fa-history"></i>
          <span>{{ showOrderHistory ? '隐藏订单' : '历史订单' }}</span>
        </button>
        <button class="nav-item" @click="scrollToSection('running-strategies')" v-if="runningStrategiesCount > 0">
          <i class="fas fa-play"></i>
          <span>运行中策略 ({{ runningStrategiesCount }})</span>
        </button>
      </div>
    </div>

    <!-- API设置模态框 -->
    <OkxApiModal
      :show="showApiModal"
      :isReconfiguring="isReconfiguring"
      @close="showApiModal = false"
      @api-validated="onApiValidated"
    />

    <!-- 删除API确认对话框 -->
    <div class="modal-overlay" v-if="showDeleteApiConfirm" @click.self="showDeleteApiConfirm = false">
      <div class="delete-confirm-modal">
        <div class="modal-header">
          <h3>确认删除API</h3>
          <button class="close-button" @click="showDeleteApiConfirm = false">&times;</button>
        </div>
        <div class="modal-body">
          <div class="warning-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <p class="warning-text">您确定要删除OKX API配置吗？</p>
          <p class="warning-details">删除后将：</p>
          <ul class="warning-list">
            <li>断开与OKX的连接</li>
            <li>停止所有运行中的策略</li>
            <li>清除已保存的API密钥</li>
            <li>需要重新配置才能使用交易功能</li>
          </ul>
        </div>
        <div class="modal-footer">
          <button class="cancel-button" @click="showDeleteApiConfirm = false">
            取消
          </button>
          <button class="confirm-delete-button" @click="deleteApi" :disabled="isDeletingApi">
            <i class="fas fa-trash"></i>
            {{ isDeletingApi ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 批量停止策略确认对话框 -->
    <div class="modal-overlay" v-if="showBatchStopConfirm" @click.self="showBatchStopConfirm = false">
      <div class="batch-confirm-modal">
        <div class="modal-header">
          <h3>确认批量停止策略</h3>
          <button class="close-button" @click="showBatchStopConfirm = false">&times;</button>
        </div>
        <div class="modal-body">
          <div class="warning-icon">
            <i class="fas fa-stop-circle"></i>
          </div>
          <p class="warning-text">您确定要停止选中的 {{ runningSelectedCount }} 个运行中的策略吗？</p>
          <div class="strategy-list-preview">
            <div v-for="strategy in selectedRunningStrategies" :key="strategy.id || strategy._id" class="strategy-preview-item">
              <span class="strategy-name">{{ strategy.strategyName || '未命名策略' }}</span>
              <span class="strategy-symbol">{{ strategy.symbol }}</span>
              <span class="strategy-status">{{ getStatusText(strategy.status) }}</span>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-button" @click="showBatchStopConfirm = false">
            取消
          </button>
          <button class="confirm-stop-button" @click="batchStopStrategies" :disabled="isBatchStopping">
            <i class="fas fa-stop"></i>
            {{ isBatchStopping ? '停止中...' : '确认停止' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 批量删除策略确认对话框 -->
    <div class="modal-overlay" v-if="showBatchDeleteConfirm" @click.self="showBatchDeleteConfirm = false">
      <div class="batch-confirm-modal">
        <div class="modal-header">
          <h3>确认批量删除策略</h3>
          <button class="close-button" @click="showBatchDeleteConfirm = false">&times;</button>
        </div>
        <div class="modal-body">
          <div class="warning-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <p class="warning-text">您确定要删除选中的 {{ nonRunningSelectedCount }} 个策略吗？</p>
          <p class="warning-details">此操作不可撤销！</p>
          <div class="strategy-list-preview">
            <div v-for="strategy in selectedNonRunningStrategies" :key="strategy.id || strategy._id" class="strategy-preview-item">
              <span class="strategy-name">{{ strategy.strategyName || '未命名策略' }}</span>
              <span class="strategy-symbol">{{ strategy.symbol }}</span>
              <span class="strategy-status">{{ getStatusText(strategy.status) }}</span>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-button" @click="showBatchDeleteConfirm = false">
            取消
          </button>
          <button class="confirm-delete-button" @click="batchDeleteStrategies" :disabled="isBatchDeleting">
            <i class="fas fa-trash"></i>
            {{ isBatchDeleting ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import OkxApiModal from '../components/OkxApiModal.vue'
import OkxStrategyForm from '../components/OkxStrategyForm.vue'
import OkxOrderHistory from '../components/OkxOrderHistory.vue'
import AccountInfoPanel from '../components/PythonTradePanel.vue'
import { io } from 'socket.io-client'
import { api } from '../api'

export default {
  name: 'OkxView',
  components: {
    OkxApiModal,
    OkxStrategyForm,
    OkxOrderHistory,
    AccountInfoPanel
  },
  setup() {
    const apiConnected = ref(false)
    const userId = ref('')
    const showApiModal = ref(false)
    const showOrderHistory = ref(false)
    const strategies = ref([])
    const isReconfiguring = ref(false)

    // 新增的响应式数据
    const viewMode = ref('card') // 'card' 或 'compact'
    const statusFilter = ref('all') // 'all', 'running', 'completed'
    const searchQuery = ref('')
    const showScrollTop = ref(false)
    const showQuickNav = ref(false)
    const showDeleteApiConfirm = ref(false)
    const isDeletingApi = ref(false)

    // 批量操作相关
    const selectedStrategies = ref([])
    const showBatchStopConfirm = ref(false)
    const showBatchDeleteConfirm = ref(false)
    const isBatchStopping = ref(false)
    const isBatchDeleting = ref(false)

    let socket = null

    // 计算属性
    const runningStrategiesCount = computed(() => {
      return strategies.value.filter(s => s.status === 'waiting' || s.status === 'active').length
    })

    const completedStrategiesCount = computed(() => {
      return strategies.value.filter(s => s.status === 'completed').length
    })

    // 排序策略：运行中的策略排在前面
    const sortedStrategies = computed(() => {
      return [...strategies.value].sort((a, b) => {
        const aIsRunning = a.status === 'waiting' || a.status === 'active'
        const bIsRunning = b.status === 'waiting' || b.status === 'active'

        if (aIsRunning && !bIsRunning) return -1
        if (!aIsRunning && bIsRunning) return 1

        // 如果都是运行中或都不是运行中，按创建时间排序（新的在前）
        return new Date(b.createdAt || 0) - new Date(a.createdAt || 0)
      })
    })

    // 筛选策略
    const filteredStrategies = computed(() => {
      let filtered = sortedStrategies.value

      // 状态筛选
      if (statusFilter.value === 'running') {
        filtered = filtered.filter(s => s.status === 'waiting' || s.status === 'active')
      } else if (statusFilter.value === 'completed') {
        filtered = filtered.filter(s => s.status === 'completed')
      }

      // 搜索筛选
      if (searchQuery.value.trim()) {
        const query = searchQuery.value.toLowerCase().trim()
        filtered = filtered.filter(s =>
          (s.strategyName && typeof s.strategyName === 'string' && s.strategyName.toLowerCase().includes(query)) ||
          (s.symbol && typeof s.symbol === 'string' && s.symbol.toLowerCase().includes(query)) ||
          (s.type && typeof s.type === 'string' && s.type.toLowerCase().includes(query))
        )
      }

      return filtered
    })

    // 批量操作相关计算属性
    const isAllSelected = computed(() => {
      return filteredStrategies.value.length > 0 && selectedStrategies.value.length === filteredStrategies.value.length
    })

    const isPartiallySelected = computed(() => {
      return selectedStrategies.value.length > 0 && selectedStrategies.value.length < filteredStrategies.value.length
    })

    const selectedRunningStrategies = computed(() => {
      return filteredStrategies.value.filter(s =>
        selectedStrategies.value.includes(s.id || s._id) &&
        (s.status === 'waiting' || s.status === 'active')
      )
    })

    const selectedNonRunningStrategies = computed(() => {
      return filteredStrategies.value.filter(s =>
        selectedStrategies.value.includes(s.id || s._id) &&
        s.status !== 'waiting' && s.status !== 'active'
      )
    })

    const runningSelectedCount = computed(() => selectedRunningStrategies.value.length)
    const nonRunningSelectedCount = computed(() => selectedNonRunningStrategies.value.length)

    const hasRunningSelectedStrategies = computed(() => runningSelectedCount.value > 0)
    const hasNonRunningSelectedStrategies = computed(() => nonRunningSelectedCount.value > 0)

    // 格式化日期
    const formatDate = (timestamp) => {
      if (!timestamp) return '-'
      const date = new Date(timestamp)
      return date.toLocaleString()
    }

    // 判断是否为运行中的策略
    const isRunningStrategy = (strategy) => {
      return strategy.status === 'waiting' || strategy.status === 'active'
    }

    // 获取无策略时的提示文本
    const getNoStrategiesText = () => {
      if (searchQuery.value.trim()) {
        return '未找到匹配的策略'
      }
      if (statusFilter.value === 'running') {
        return '暂无运行中的策略'
      }
      if (statusFilter.value === 'completed') {
        return '暂无已完成的策略'
      }
      return '暂无策略'
    }

    // 滚动到顶部
    const scrollToTop = () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
      showQuickNav.value = false
    }

    // 滚动到指定区域
    const scrollToSection = (sectionId) => {
      let targetElement = null

      switch (sectionId) {
        case 'account-info':
          // 滚动到账户信息面板
          targetElement = document.querySelector('.api-status-section') ||
                         document.querySelector('.account-info-panel')
          break
        case 'strategy-form':
          // 滚动到策略创建表单
          targetElement = document.querySelector('.strategy-form')
          break
        case 'strategies-list':
          // 滚动到策略列表
          targetElement = document.querySelector('.active-strategies')
          break
        case 'running-strategies':
          // 滚动到第一个运行中的策略
          targetElement = document.querySelector('.strategy-card .status-active, .strategy-card .status-waiting')?.closest('.strategy-card') ||
                         document.querySelector('.compact-strategy-row.running-strategy')
          break
        default:
          return
      }

      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
      }
      showQuickNav.value = false
    }

    // 监听滚动事件
    const handleScroll = () => {
      showScrollTop.value = window.scrollY > 300

      // 点击其他地方关闭快速导航菜单
      if (showQuickNav.value && window.scrollY > 0) {
        showQuickNav.value = false
      }
    }

    // 切换历史订单显示并自动滚动
    const toggleOrderHistory = () => {
      showOrderHistory.value = !showOrderHistory.value

      // 如果是显示历史订单，等待DOM更新后滚动到历史订单区域
      if (showOrderHistory.value) {
        // 使用nextTick确保DOM已更新
        setTimeout(() => {
          const orderHistoryElement = document.getElementById('order-history-section')
          if (orderHistoryElement) {
            orderHistoryElement.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            })
          }
        }, 100) // 给一点时间让组件渲染
      }
    }

    // 保存用户状态到本地存储
    const saveUserState = () => {
      localStorage.setItem('userId', userId.value);
      localStorage.setItem(`apiConnected_${userId.value}`, apiConnected.value.toString());
      console.log('用户状态已保存到本地存储，用户ID:', userId.value);
    };

    // 从本地存储加载用户状态
    const loadUserState = async () => {
      // 首先从登录用户信息中获取UID
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          if (user && user.uid) {
            console.log('从登录用户信息加载用户UID:', user.uid);
            userId.value = user.uid;
          }
        } catch (error) {
          console.error('解析用户信息失败:', error);
        }
      }

      // 如果没有登录用户信息，尝试从旧的存储中获取
      if (!userId.value) {
        const savedUserId = localStorage.getItem('userId');
        if (savedUserId) {
          console.log('从本地存储加载用户ID:', savedUserId);
          userId.value = savedUserId;
        } else {
          console.log('未找到用户ID，使用默认值');
          userId.value = 'default';
        }
      }

      console.log('最终确定的用户ID:', userId.value);

      // 检查服务器端的API密钥状态
      await checkApiKeyStatus();
    };

    // 检查API密钥状态
    const checkApiKeyStatus = async () => {
      try {
        console.log('检查API密钥状态...');
        const response = await api.get('/okx/api-keys');

        if (response.data.success && response.data.hasApiKeys) {
          console.log('✅ 真实OKX API密钥已配置:', response.data.apiKeyPreview);
          apiConnected.value = true;
          // 保存API连接状态到本地存储
          localStorage.setItem(`apiConnected_${userId.value}`, 'true');
          // 连接WebSocket
          connectWebSocket();
          // 获取策略列表
          await fetchStrategies();
        } else {
          console.log('❌ 真实OKX API密钥未配置，必须配置才能使用真实交易功能');
          apiConnected.value = false;
          localStorage.setItem(`apiConnected_${userId.value}`, 'false');
          // 设置为首次配置模式
          isReconfiguring.value = false;
          // 显示API配置弹窗
          showApiModal.value = true;
          // 显示强制配置提示
          ElMessage.warning('本系统仅支持真实交易，请配置有效的OKX API密钥');
        }
      } catch (error) {
        console.error('检查API密钥状态失败:', error);
        // 如果检查失败，回退到本地存储检查
        const savedApiConnected = localStorage.getItem(`apiConnected_${userId.value}`);
        if (savedApiConnected === 'true') {
          console.log('从本地存储加载API连接状态: 已连接');
          apiConnected.value = true;
          connectWebSocket();
        } else {
          console.log('API未连接，需要用户输入API密钥');
          apiConnected.value = false;
          isReconfiguring.value = false;
          showApiModal.value = true;
        }
      }
    };

    // API验证成功回调
    const onApiValidated = async (apiKeys) => {
      console.log('API验证成功，更新状态');
      apiConnected.value = true;
      showApiModal.value = false;

      // 重置重新配置标志
      isReconfiguring.value = false;

      // 保存API连接状态
      localStorage.setItem(`apiConnected_${userId.value}`, 'true');
      saveUserState();

      // 连接WebSocket
      connectWebSocket();

      // 清空策略列表
      strategies.value = [];

      // 获取最新策略列表
      await fetchStrategies();
    }

    // 策略创建成功回调
    const onStrategyCreated = (strategyData) => {
      // 添加新策略到列表
      strategies.value.unshift({
        ...strategyData,
        createdAt: Date.now()
      });
    }

    // 连接WebSocket
    const connectWebSocket = () => {
      console.log('正在连接WebSocket...');
      // 使用绝对URL确保连接到正确的后端服务器，并添加额外的连接选项
      const wsUrl = import.meta.env.VITE_WS_URL || 'http://localhost:3009';
      console.log('使用WebSocket URL:', wsUrl);
      socket = io(wsUrl, {
        transports: ['websocket', 'polling'],
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        timeout: 20000
      })

      socket.on('connect', () => {
        console.log('WebSocket connected')

        // 订阅OKX更新
        socket.emit('subscribeOkx', { userId: userId.value })
      })

      socket.on('okxUpdate', (data) => {
        console.log('收到OKX更新:', data)
      })

      socket.on('okxAccountInit', (data) => {
        console.log('收到OKX账户初始化数据:', data)
      })

      socket.on('disconnect', () => {
        console.log('WebSocket disconnected')
      })
    }

    // 获取策略列表
    const fetchStrategies = async () => {
      try {
        console.log('正在获取策略列表，用户ID:', userId.value);

        // 构建请求参数，包含用户ID
        const params = {};
        if (userId.value && userId.value !== 'default') {
          params.userId = userId.value;
        }

        // 调用策略API，传递用户ID参数
        const response = await api.get('/test-strategies', { params });
        console.log('策略API响应:', response.data);

        if (response.data && response.data.success && response.data.strategies) {
          console.log(`获取到 ${response.data.strategies.length} 个策略，用户: ${response.data.userId || userId.value}`);

          // 检查每个策略的状态和盈利数据
          response.data.strategies.forEach((strategy, index) => {
            console.log(`策略 ${index + 1}:`, {
              id: strategy.id || strategy._id,
              name: strategy.strategyName,
              status: strategy.status,
              type: strategy.type,
              exchange: strategy.exchange || 'okx',
              currentPrice: strategy.currentPrice,
              profit: strategy.profit,
              profitPercentage: strategy.profitPercentage,
              orders: strategy.orders ? strategy.orders.length : 0,
              entryPrice: strategy.entryPrice
            });
          });

          strategies.value = response.data.strategies;
        } else {
          console.error('获取策略列表失败: 返回数据格式不正确', response.data);
          // 如果获取失败，清空策略列表
          strategies.value = [];
        }
      } catch (error) {
        console.error('获取策略列表失败:', error);
        if (error.response) {
          console.error('错误响应:', error.response.data);
        }
        // 如果请求失败，清空策略列表
        strategies.value = [];
      }
    }

    // 停止策略
    const stopStrategy = async (strategyId) => {
      try {
        if (!strategyId) {
          console.error('策略ID不能为空')
          alert('策略ID不能为空')
          return
        }

        console.log('正在停止策略:', strategyId)

        const response = await api.post(`/strategies/${strategyId}/stop`)

        console.log('停止策略响应:', response.data)

        if (response.data && response.data.success) {
          console.log('策略停止成功')
          alert('策略已停止')

          // 立即更新本地策略状态
          const index = strategies.value.findIndex(s => (s.id === strategyId || s._id === strategyId))
          if (index !== -1) {
            strategies.value[index].status = 'completed'
            console.log('本地策略状态已更新为completed')
          }

          // 重新获取策略列表以确保数据同步
          await fetchStrategies()
        } else {
          console.error('停止策略失败:', response.data.error || '未知错误')
          alert('停止策略失败: ' + (response.data.error || '未知错误'))
        }
      } catch (error) {
        console.error('停止策略失败:', error)
        alert('停止策略失败: ' + (error.response?.data?.error || error.message || '网络错误'))
      }
    }

    // 恢复策略
    const recoverStrategy = async (strategyId) => {
      try {
        if (!strategyId) {
          console.error('策略ID不能为空')
          return
        }

        // 确认恢复操作
        if (!confirm('确定要恢复此策略吗？系统将重新启动策略监控，继续执行交易。')) {
          return
        }

        console.log('正在恢复策略:', strategyId)

        const response = await api.post(`/strategies/${strategyId}/recover`)

        console.log('恢复策略响应:', response.data)

        if (response.data && response.data.success) {
          console.log('策略恢复成功')
          alert('策略已恢复，正在重新启动监控')

          // 立即更新本地策略状态
          const index = strategies.value.findIndex(s => (s.id === strategyId || s._id === strategyId))
          if (index !== -1) {
            strategies.value[index].status = 'waiting'
            console.log('本地策略状态已更新为waiting')
          }

          // 重新获取策略列表以确保数据同步
          await fetchStrategies()
        } else {
          console.error('恢复策略失败:', response.data.error || '未知错误')
          alert('恢复策略失败: ' + (response.data.error || '未知错误'))
        }
      } catch (error) {
        console.error('恢复策略失败:', error)
        alert('恢复策略失败: ' + (error.response?.data?.error || error.message || '网络错误'))
      }
    }

    // 删除策略
    const deleteStrategy = async (strategyId) => {
      try {
        if (!strategyId) {
          console.error('策略ID不能为空')
          return
        }

        if (!confirm('确定要删除此策略吗？此操作不可撤销。')) {
          return
        }

        console.log('删除策略:', strategyId)
        const response = await api.delete(`/strategies/${strategyId}`)

        if (response.data && response.data.success) {
          console.log('删除策略成功')
          // 刷新策略列表
          await fetchStrategies()
        } else {
          console.error('删除策略失败:', response.data.error)
        }
      } catch (error) {
        console.error('删除策略失败:', error)
        if (error.response) {
          console.error('错误响应:', error.response.data)
        }
      }
    }

    // 获取状态文本
    const getStatusText = (status) => {
      switch (status) {
        case 'waiting':
          return '等待中'
        case 'active':
          return '运行中'
        case 'completed':
          return '已完成'
        case 'executing':
          return '执行中'
        case 'error':
          return '错误'
        default:
          return status || '未知'
      }
    }

    // 获取状态样式类
    const getStatusClass = (status) => {
      switch (status) {
        case 'waiting':
          return 'status-waiting'
        case 'active':
          return 'status-active'
        case 'completed':
          return 'status-completed'
        case 'executing':
          return 'status-executing'
        case 'error':
          return 'status-error'
        default:
          return ''
      }
    }

    // 获取方向文本
    const getDirectionText = (direction, type) => {
      switch (direction) {
        case 'long':
          return type === 'spot' ? '买入' : '做多'
        case 'short':
          return '做空'
        case 'both':
          return '自动判断'
        default:
          return direction || '未知'
      }
    }

    // 获取金额标签
    const getAmountLabel = (strategy) => {
      if (isDynamicPositionStrategy(strategy)) {
        return '总资金'
      }
      return '开仓金额'
    }

    // 判断是否为动态仓位策略
    const isDynamicPositionStrategy = (strategy) => {
      const dynamicStrategies = ['futures1', 'futures2', 'futures3', 'futures4']
      return strategy && dynamicStrategies.includes(strategy.strategyTemplate)
    }

    // 重新配置API
    const reconfigureApi = () => {
      console.log('用户请求重新配置API');

      // 确认用户是否真的要重新配置
      if (confirm('确定要重新配置API密钥吗？这将断开当前连接并需要重新输入API信息。')) {
        // 断开WebSocket连接
        if (socket) {
          socket.disconnect();
          socket = null;
        }

        // 重置API连接状态
        apiConnected.value = false;
        localStorage.setItem(`apiConnected_${userId.value}`, 'false');

        // 清空策略列表
        strategies.value = [];

        // 设置为重新配置模式
        isReconfiguring.value = true;

        // 显示API配置弹窗
        showApiModal.value = true;

        console.log('API状态已重置，等待用户重新配置');
      }
    }

    // 删除API配置
    const deleteApi = async () => {
      try {
        isDeletingApi.value = true;
        console.log('开始删除OKX API配置...');

        // 停止所有运行中的策略
        const runningStrategies = strategies.value.filter(s => s.status === 'waiting' || s.status === 'active');
        if (runningStrategies.length > 0) {
          console.log(`正在停止 ${runningStrategies.length} 个运行中的策略...`);
          for (const strategy of runningStrategies) {
            try {
              await api.post(`/strategies/${strategy.id || strategy._id}/stop`);
            } catch (error) {
              console.error(`停止策略 ${strategy.id || strategy._id} 失败:`, error);
            }
          }
        }

        // 调用后端删除API接口
        const response = await api.delete('/okx/api-keys');

        if (response.data.success) {
          console.log('OKX API配置删除成功');

          // 断开WebSocket连接
          if (socket) {
            socket.disconnect();
            socket = null;
          }

          // 重置所有状态
          apiConnected.value = false;
          localStorage.setItem(`apiConnected_${userId.value}`, 'false');
          strategies.value = [];
          showDeleteApiConfirm.value = false;

          // 显示成功消息
          alert('OKX API配置已成功删除！');
        } else {
          console.error('删除API配置失败:', response.data.error);
          alert('删除API配置失败: ' + (response.data.error || '未知错误'));
        }
      } catch (error) {
        console.error('删除API配置失败:', error);
        alert('删除API配置失败: ' + (error.response?.data?.error || error.message || '网络错误'));
      } finally {
        isDeletingApi.value = false;
      }
    }

    // 计算盈利金额
    const calculateProfitAmount = (strategy) => {
      if (!strategy) return '0.00';

      // 优先使用后端计算的profit字段
      if (strategy.profit !== undefined && strategy.profit !== null) {
        const profit = Number(strategy.profit);
        if (isNaN(profit)) return '0.00';
        return profit.toFixed(2);
      }

      // 如果没有profit字段，但有订单和当前价格，则前端计算
      if (strategy.orders && strategy.orders.length > 0 && strategy.currentPrice) {
        const buyOrders = strategy.orders.filter(order => order.side === 'buy');
        if (buyOrders.length === 0) return '0.00';

        const totalCost = buyOrders.reduce((sum, order) => sum + (Number(order.price) * Number(order.amount)), 0);
        const totalAmount = buyOrders.reduce((sum, order) => sum + Number(order.amount), 0);
        const currentValue = totalAmount * Number(strategy.currentPrice);
        const profit = currentValue - totalCost;

        if (isNaN(profit)) return '0.00';
        return profit.toFixed(2);
      }

      return '0.00';
    }

    // 计算盈利百分比
    const calculateProfitPercentage = (strategy) => {
      if (!strategy) return '(0.00%)';

      // 优先使用后端计算的profitPercentage字段
      if (strategy.profitPercentage !== undefined && strategy.profitPercentage !== null) {
        const percentage = Number(strategy.profitPercentage);
        if (isNaN(percentage)) return '(0.00%)';
        const sign = percentage > 0 ? '+' : '';
        return `(${sign}${percentage.toFixed(2)}%)`;
      }

      // 如果没有profitPercentage字段，但有订单和当前价格，则前端计算
      if (strategy.orders && strategy.orders.length > 0 && strategy.currentPrice) {
        const buyOrders = strategy.orders.filter(order => order.side === 'buy');
        if (buyOrders.length === 0) return '(0.00%)';

        const totalCost = buyOrders.reduce((sum, order) => sum + (Number(order.price) * Number(order.amount)), 0);
        const totalAmount = buyOrders.reduce((sum, order) => sum + Number(order.amount), 0);
        const currentValue = totalAmount * Number(strategy.currentPrice);

        if (totalCost === 0) return '(0.00%)';

        const profitPercentage = ((currentValue - totalCost) / totalCost) * 100;

        if (isNaN(profitPercentage)) return '(0.00%)';

        const sign = profitPercentage > 0 ? '+' : '';
        return `(${sign}${profitPercentage.toFixed(2)}%)`;
      }

      return '(0.00%)';
    }

    // 获取盈利样式类
    const getProfitClass = (strategy) => {
      const profitAmount = parseFloat(calculateProfitAmount(strategy));
      if (profitAmount > 0) {
        return 'profit-positive';
      } else if (profitAmount < 0) {
        return 'profit-negative';
      }
      return '';
    }

    // 检查策略是否有持仓
    const hasPosition = (strategy) => {
      if (!strategy) return false;

      // 检查是否有买入订单
      if (strategy.orders && strategy.orders.length > 0) {
        const buyOrders = strategy.orders.filter(order => order.side === 'buy');
        return buyOrders.length > 0;
      }

      // 检查是否有入场价格
      if (strategy.entryPrice && strategy.entryPrice > 0) {
        return true;
      }

      return false;
    }

    // 一键平仓功能
    const closePosition = async (strategyId) => {
      try {
        if (!strategyId) {
          console.error('策略ID不能为空');
          alert('策略ID不能为空');
          return;
        }

        // 确认操作
        if (!confirm('确定要立即平仓所有持仓吗？此操作将卖出所有持有的币种。')) {
          return;
        }

        console.log('正在执行一键平仓:', strategyId);

        const response = await api.post(`/strategies/${strategyId}/close-position`);

        console.log('一键平仓响应:', response.data);

        if (response.data && response.data.success) {
          console.log('一键平仓成功');

          // 检查是否是自动修复的情况
          if (response.data.autoFixed) {
            alert(`平仓完成！\n\n检测到账户余额已为0：\n- 该币种可能已被手动卖出或之前已平仓\n- 策略记录已自动同步\n- 策略状态已设为完成\n\n建议：可通过"查询余额"功能确认账户状态`);
          } else {
            alert('平仓成功！所有持仓已卖出。');
          }

          // 立即更新本地策略状态
          const index = strategies.value.findIndex(s => (s.id === strategyId || s._id === strategyId));
          if (index !== -1) {
            strategies.value[index].status = 'completed';
            console.log('本地策略状态已更新为completed');
          }

          // 重新获取策略列表以确保数据同步
          await fetchStrategies();
        } else {
          console.error('一键平仓失败:', response.data.error || '未知错误');

          // 提供更友好的错误信息
          let errorMessage = response.data.error || '未知错误';

          // 检查是否是数据不一致的错误
          if (errorMessage.includes('没有可平仓的持仓') && errorMessage.includes('实际余额: 0')) {
            errorMessage += '\n\n💡 建议：\n1. 该币种可能已被手动卖出\n2. 可以尝试删除此策略\n3. 或联系技术支持处理数据同步问题';
          }

          alert('一键平仓失败: ' + errorMessage);
        }
      } catch (error) {
        console.error('一键平仓失败:', error);
        alert('一键平仓失败: ' + (error.response?.data?.error || error.message || '网络错误'));
      }
    }

    // 批量操作方法
    const toggleSelectAll = () => {
      if (isAllSelected.value) {
        selectedStrategies.value = []
      } else {
        selectedStrategies.value = filteredStrategies.value.map(s => s.id || s._id)
      }
    }

    const batchStopStrategies = async () => {
      if (selectedRunningStrategies.value.length === 0) {
        alert('没有选中的运行中策略')
        return
      }

      try {
        isBatchStopping.value = true
        const strategyIds = selectedRunningStrategies.value.map(s => s.id || s._id)

        console.log('批量停止策略:', strategyIds)
        const response = await api.post('/okx/strategies/batch-stop', { strategyIds })

        if (response.data && response.data.success) {
          console.log('批量停止策略成功')
          alert(`成功停止 ${response.data.stoppedCount} 个策略`)

          // 清空选择
          selectedStrategies.value = []
          showBatchStopConfirm.value = false

          // 刷新策略列表
          await fetchStrategies()
        } else {
          console.error('批量停止策略失败:', response.data.error)
          alert('批量停止策略失败: ' + (response.data.error || '未知错误'))
        }
      } catch (error) {
        console.error('批量停止策略失败:', error)
        alert('批量停止策略失败: ' + (error.response?.data?.error || error.message || '网络错误'))
      } finally {
        isBatchStopping.value = false
      }
    }

    const batchDeleteStrategies = async () => {
      if (selectedNonRunningStrategies.value.length === 0) {
        alert('没有选中的可删除策略')
        return
      }

      try {
        isBatchDeleting.value = true
        const strategyIds = selectedNonRunningStrategies.value.map(s => s.id || s._id)

        console.log('批量删除策略:', strategyIds)
        const response = await api.post('/okx/strategies/batch-delete', { strategyIds })

        if (response.data && response.data.success) {
          console.log('批量删除策略成功')
          alert(`成功删除 ${response.data.deletedCount} 个策略`)

          // 清空选择
          selectedStrategies.value = []
          showBatchDeleteConfirm.value = false

          // 刷新策略列表
          await fetchStrategies()
        } else {
          console.error('批量删除策略失败:', response.data.error)
          alert('批量删除策略失败: ' + (response.data.error || '未知错误'))
        }
      } catch (error) {
        console.error('批量删除策略失败:', error)
        alert('批量删除策略失败: ' + (error.response?.data?.error || error.message || '网络错误'))
      } finally {
        isBatchDeleting.value = false
      }
    }

    // 定时刷新策略数据
    let refreshInterval = null;

    onMounted(async () => {
      console.log('组件挂载，开始初始化...');

      // 加载用户状态（包括检查API密钥状态）
      await loadUserState();

      console.log('初始化完成，策略数量:', strategies.value.length);

      // 设置定时刷新，每30秒刷新一次策略数据
      refreshInterval = setInterval(async () => {
        if (apiConnected.value && strategies.value.length > 0) {
          console.log('定时刷新策略数据...');
          await fetchStrategies();
        }
      }, 30000); // 30秒刷新一次

      // 添加滚动事件监听
      window.addEventListener('scroll', handleScroll);
    })

    onUnmounted(() => {
      // 断开WebSocket连接
      if (socket) {
        socket.disconnect()
      }

      // 清理定时器
      if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
      }

      // 移除滚动事件监听
      window.removeEventListener('scroll', handleScroll);
    })

    return {
      apiConnected,
      userId,
      showApiModal,
      showOrderHistory,
      strategies,
      isReconfiguring,
      // 新增的响应式数据
      viewMode,
      statusFilter,
      searchQuery,
      showScrollTop,
      showQuickNav,
      showDeleteApiConfirm,
      isDeletingApi,
      // 批量操作相关
      selectedStrategies,
      showBatchStopConfirm,
      showBatchDeleteConfirm,
      isBatchStopping,
      isBatchDeleting,
      // 计算属性
      runningStrategiesCount,
      completedStrategiesCount,
      filteredStrategies,
      isAllSelected,
      isPartiallySelected,
      selectedRunningStrategies,
      selectedNonRunningStrategies,
      runningSelectedCount,
      nonRunningSelectedCount,
      hasRunningSelectedStrategies,
      hasNonRunningSelectedStrategies,
      // 方法
      formatDate,
      isRunningStrategy,
      getNoStrategiesText,
      scrollToTop,
      scrollToSection,
      toggleOrderHistory,
      onApiValidated,
      onStrategyCreated,
      stopStrategy,
      recoverStrategy,
      deleteStrategy,
      getStatusText,
      getStatusClass,
      getDirectionText,
      getAmountLabel,
      isDynamicPositionStrategy,
      fetchStrategies,
      reconfigureApi,
      deleteApi,
      calculateProfitAmount,
      calculateProfitPercentage,
      getProfitClass,
      hasPosition,
      closePosition,
      toggleSelectAll,
      batchStopStrategies,
      batchDeleteStrategies
    }
  }
}
</script>

<style scoped>
.okx-view {
  min-height: 100vh;
  overflow-y: auto;
}

.market-header {
  background-color: #0a6e6a;
  color: white;
  padding: 15px 20px;
  border-radius: 0 0 15px 15px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.main-content {
  padding: 20px;
  padding-bottom: 100px; /* 增加底部内边距，确保底部内容可见 */
  min-height: calc(100vh - 120px); /* 确保页面有足够的高度 */
}

.api-status-section {
  background-color: white;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.api-status-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.api-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.api-status-text {
  color: #4caf50;
  font-weight: bold;
  font-size: 1rem;
}

.reconfigure-button {
  background-color: #ff9800;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s;
}

.reconfigure-button:hover {
  background-color: #f57c00;
}

.delete-api-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  gap: 5px;
}

.delete-api-button:hover {
  background-color: #d32f2f;
}

.delete-api-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.connect-api-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.card {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  width: 100%;
  max-width: 400px;
}

.card-content h3 {
  margin-bottom: 10px;
  color: #333;
}

.card-content p {
  color: #666;
  margin-bottom: 20px;
}

.connect-button {
  background-color: #0a6e6a;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
}

.section-title {
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.active-strategies {
  background-color: white;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.no-strategies {
  text-align: center;
  padding: 20px;
  color: #999;
}

.strategies-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
  margin-bottom: 20px; /* 确保与下方内容有间距 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .strategies-list {
    grid-template-columns: 1fr; /* 小屏幕上单列显示 */
    gap: 10px;
  }

  .main-content {
    padding: 15px;
    padding-bottom: 120px; /* 小屏幕上增加更多底部空间 */
  }

  .api-status-info {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .history-orders-section {
    margin: 20px 0;
    padding: 15px 0;
  }
}

.strategy-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  overflow: hidden;
}

.strategy-header {
  background-color: #0a6e6a;
  color: white;
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.network-error-warning {
  background-color: #fff3cd;
  color: #856404;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  border: 1px solid #ffeaa7;
  display: flex;
  align-items: center;
  gap: 4px;
}

.network-error-warning i {
  color: #f39c12;
}

.strategy-status {
  color: white;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.status-waiting {
  background-color: #03a9f4; /* 浅蓝色 */
}

.status-active {
  background-color: #4caf50; /* 绿色 */
}

.status-completed {
  background-color: #2196F3; /* 蓝色 */
}

.status-executing {
  background-color: #ff9800; /* 橙色 */
}

.status-error {
  background-color: #f44336; /* 红色 */
}

.strategy-details {
  padding: 15px;
}

.strategy-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.strategy-item .label {
  color: #666;
  font-size: 0.9rem;
}

.strategy-item .value {
  font-weight: bold;
  color: #333;
}

.strategy-name {
  margin-bottom: 12px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #ddd;
}

.strategy-name .value {
  font-weight: bold;
  color: #0a6e6a;
  font-size: 1rem;
  text-align: center;
  width: 100%;
}

.history-orders-section {
  display: flex;
  justify-content: center;
  margin: 30px 0; /* 增加上下边距 */
  padding: 20px 0; /* 增加内边距 */
}

.history-button {
  background-color: #0a6e6a;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.history-button:hover {
  background-color: #085d59;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.history-button i {
  margin-right: 8px;
}

.strategy-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px dashed #ddd;
}

.action-buttons {
  flex: 1;
  display: flex;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
}

.delete-action {
  display: flex;
  justify-content: flex-end;
}

.stop-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s;
}

.stop-button:hover {
  background-color: #d32f2f;
}

.close-position-button {
  background-color: #ff9800;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 5px;
}

.close-position-button:hover {
  background-color: #f57c00;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.close-position-button i {
  font-size: 0.8rem;
}

.recover-button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 5px;
  margin-right: 10px;
}

.recover-button:hover {
  background-color: #45a049;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.recover-button i {
  font-size: 0.8rem;
}

.delete-button {
  background-color: #ff5722;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 5px;
}

.delete-button:hover {
  background-color: #e64a19;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.delete-button i {
  font-size: 0.8rem;
}

.strategy-status-info {
  color: #666;
  font-size: 0.9rem;
}

.profit-positive {
  color: #4caf50;
  font-weight: bold;
}

.profit-negative {
  color: #f44336;
  font-weight: bold;
}

.risk-value {
  color: #f44336;
  font-weight: bold;
}

.profit-value {
  color: #4caf50;
  font-weight: bold;
}

.delete-button-small {
  background-color: transparent;
  color: #999;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all 0.3s;
}

.delete-button-small:hover {
  background-color: #f44336;
  color: white;
}

.delete-icon {
  font-size: 18px;
  font-weight: bold;
  line-height: 1;
}

/* 动态仓位显示样式 */
.dynamic-position-display {
  background-color: #e3f2fd;
  border-radius: 6px;
  padding: 8px 12px;
  margin: 8px 0;
  border-left: 4px solid #2196F3;
}

.dynamic-position-value {
  color: #1976D2 !important;
  font-weight: bold;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.position-method {
  font-size: 0.75rem;
  color: #666 !important;
  font-weight: normal;
  margin-top: 2px;
}

.calculating {
  color: #ff9800 !important;
  font-style: italic;
}

/* 盈利显示样式 */
.profit-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.profit-amount {
  font-weight: bold;
  font-size: 1rem;
}

.profit-percentage {
  font-size: 0.85rem;
  font-weight: normal;
}

.profit-positive .profit-amount,
.profit-positive .profit-percentage {
  color: #4caf50 !important;
}

.profit-negative .profit-amount,
.profit-negative .profit-percentage {
  color: #f44336 !important;
}

/* 浮动操作按钮样式 */
.floating-actions {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10px;
}

.floating-btn-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.floating-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
}

.floating-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.scroll-top-btn {
  background: linear-gradient(135deg, #0a6e6a, #0d8a85);
  color: white;
}

.scroll-top-btn:hover {
  background: linear-gradient(135deg, #085d59, #0a7570);
}

.nav-btn {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
}

.nav-btn:hover {
  background: linear-gradient(135deg, #f57c00, #ef6c00);
}

/* 快速导航菜单 */
.quick-nav-menu {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  padding: 8px;
  margin-bottom: 10px;
  min-width: 180px;
  border: 1px solid #e0e0e0;
}

.nav-item {
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #333;
}

.nav-item:hover {
  background-color: #f5f5f5;
  color: #0a6e6a;
}

.nav-item i {
  width: 16px;
  text-align: center;
  color: #666;
}

.nav-item:hover i {
  color: #0a6e6a;
}

.nav-item span {
  flex: 1;
  text-align: left;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .floating-actions {
    bottom: 15px;
    right: 15px;
  }

  .floating-btn {
    width: 45px;
    height: 45px;
    font-size: 16px;
  }

  .quick-nav-menu {
    min-width: 160px;
    margin-right: -10px;
  }

  .nav-item {
    padding: 10px 14px;
    font-size: 13px;
  }
}

/* 控制面板样式 */
.controls-panel {
  background: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.controls-row {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.control-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.view-toggle {
  display: flex;
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}

.view-toggle button {
  padding: 8px 16px;
  border: none;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.view-toggle button.active {
  background: #0a6e6a;
  color: white;
}

.view-toggle button:hover:not(.active) {
  background: #f5f5f5;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  min-width: 120px;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  min-width: 200px;
}

.search-input:focus {
  outline: none;
  border-color: #0a6e6a;
  box-shadow: 0 0 0 2px rgba(10, 110, 106, 0.1);
}

/* 紧凑视图样式 */
.compact-strategies-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.compact-strategies-table th {
  background: #f8f9fa;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #dee2e6;
}

.compact-strategies-table td {
  padding: 12px;
  border-bottom: 1px solid #f1f3f4;
  vertical-align: middle;
}

.compact-strategy-row:hover {
  background-color: #f8f9fa;
}

.compact-strategy-row.running-strategy {
  background-color: #f0f8ff;
}

.compact-strategy-row.running-strategy:hover {
  background-color: #e6f3ff;
}

.compact-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.compact-actions {
  display: flex;
  gap: 5px;
}

.compact-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.2s;
}

.compact-btn:hover {
  transform: translateY(-1px);
}

/* OKX界面状态筛选按钮美化 */
.strategies-header {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.strategies-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.status-filter {
  display: flex;
  gap: 8px;
  background: #f8f9fa;
  padding: 6px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-btn {
  background: transparent;
  border: none;
  padding: 12px 18px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: #666;
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

.filter-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #0a6e6a 0%, #0d8a85 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.filter-btn i {
  font-size: 13px;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  color: #333;
  background: rgba(10, 110, 106, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(10, 110, 106, 0.2);
}

.filter-btn:hover i {
  opacity: 1;
  transform: scale(1.1);
}

.filter-btn.active {
  background: linear-gradient(135deg, #0a6e6a 0%, #0d8a85 100%);
  color: white;
  box-shadow: 0 6px 16px rgba(10, 110, 106, 0.3);
  transform: translateY(-2px);
}

.filter-btn.active::before {
  opacity: 1;
}

.filter-btn.active i {
  opacity: 1;
  color: white;
}

/* 数字徽章样式 */
.count-badge {
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 700;
  margin-left: 4px;
  transition: all 0.3s ease;
  min-width: 24px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-btn.active .count-badge {
  background: rgba(255, 255, 255, 0.25);
  color: white;
  border-color: rgba(255, 255, 255, 0.2);
}

.filter-btn:hover .count-badge {
  background: rgba(10, 110, 106, 0.15);
  color: #333;
  border-color: rgba(10, 110, 106, 0.2);
}

.filter-btn.active:hover .count-badge {
  background: rgba(255, 255, 255, 0.35);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

/* 运行中策略特殊高亮 */
.filter-btn.running-highlight {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  box-shadow: 0 6px 16px rgba(40, 167, 69, 0.3);
  animation: runningPulse 2s infinite;
}

.filter-btn.running-highlight .count-badge {
  background: rgba(255, 255, 255, 0.25);
  color: white;
  border-color: rgba(255, 255, 255, 0.2);
}

@keyframes runningPulse {
  0%, 100% {
    box-shadow: 0 6px 16px rgba(40, 167, 69, 0.3);
  }
  50% {
    box-shadow: 0 8px 24px rgba(40, 167, 69, 0.5);
  }
}

/* 历史订单按钮美化 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-action-btn {
  background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 6px 16px rgba(23, 162, 184, 0.3);
  position: relative;
  overflow: hidden;
  min-width: 140px;
  justify-content: center;
}

.header-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.header-action-btn:hover::before {
  left: 100%;
}

.header-action-btn:hover {
  background: linear-gradient(135deg, #138496 0%, #1e7e34 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(23, 162, 184, 0.4);
}

.header-action-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

.header-action-btn i {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.header-action-btn:hover i {
  transform: rotate(360deg);
}

/* 按钮切换动画 */
@keyframes buttonPulse {
  0% {
    transform: translateY(-2px) scale(1);
  }
  50% {
    transform: translateY(-2px) scale(1.05);
  }
  100% {
    transform: translateY(-2px) scale(1);
  }
}

.filter-btn.active {
  animation: buttonPulse 0.4s ease-out;
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .strategies-controls {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .status-filter {
    justify-content: center;
    flex-wrap: wrap;
    padding: 4px;
    gap: 6px;
  }

  .filter-btn {
    font-size: 12px;
    padding: 10px 14px;
    gap: 6px;
    min-width: 100px;
  }

  .filter-btn i {
    font-size: 11px;
  }

  .count-badge {
    font-size: 10px;
    padding: 2px 6px;
    min-width: 20px;
  }

  .header-action-btn {
    padding: 8px 12px; /* 稍微增加padding以容纳文字 */
    font-size: 12px; /* 缩小整体字体 */
    min-width: 100px; /* 减小最小宽度 */
    gap: 6px; /* 减小图标和文字间距 */
  }

  .header-action-btn .btn-text {
    display: inline; /* 保持显示文字 */
    font-size: 12px; /* 缩小字体 */
  }

  .header-action-btn i {
    font-size: 12px; /* 缩小图标 */
  }
}

/* 删除API确认对话框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.delete-confirm-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.modal-header {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 30px;
  text-align: center;
}

.warning-icon {
  font-size: 48px;
  color: #ff9800;
  margin-bottom: 20px;
}

.warning-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.warning-details {
  color: #666;
  margin-bottom: 10px;
  font-weight: 500;
}

.warning-list {
  text-align: left;
  color: #666;
  margin: 15px 0;
  padding-left: 20px;
}

.warning-list li {
  margin-bottom: 8px;
  position: relative;
}

.warning-list li::before {
  content: '⚠️';
  position: absolute;
  left: -20px;
  top: 0;
}

.modal-footer {
  padding: 20px 30px;
  background-color: #f8f9fa;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.cancel-button {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s;
}

.cancel-button:hover {
  background-color: #5a6268;
}

.confirm-delete-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.confirm-delete-button:hover:not(:disabled) {
  background-color: #d32f2f;
}

.confirm-delete-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .delete-confirm-modal {
    width: 95%;
    margin: 20px;
  }

  .modal-body {
    padding: 20px;
  }

  .modal-footer {
    padding: 15px 20px;
    flex-direction: column;
  }

  .cancel-button,
  .confirm-delete-button {
    width: 100%;
    justify-content: center;
  }

  .api-actions {
    flex-direction: column;
    gap: 8px;
  }

  .delete-api-button,
  .reconfigure-button {
    width: 100%;
    justify-content: center;
  }
}

/* 批量操作样式 */
.batch-operations {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.batch-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.selection-controls {
  display: flex;
  align-items: center;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.checkbox-container input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 4px;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
  background-color: #0a6e6a;
  border-color: #0a6e6a;
}

.checkbox-container input[type="checkbox"]:indeterminate + .checkmark {
  background-color: #ff9800;
  border-color: #ff9800;
}

.checkmark::after {
  content: '';
  position: absolute;
  display: none;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
  display: block;
}

.checkbox-container input[type="checkbox"]:indeterminate + .checkmark::after {
  display: none;
}

.checkbox-container input[type="checkbox"]:indeterminate + .checkmark::before {
  content: '';
  position: absolute;
  left: 3px;
  top: 8px;
  width: 10px;
  height: 2px;
  background-color: white;
}

.checkbox-label {
  font-weight: 500;
  color: #333;
}

.batch-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.batch-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 5px;
}

.stop-batch-btn {
  background-color: #ff9800;
  color: white;
}

.stop-batch-btn:hover:not(:disabled) {
  background-color: #f57c00;
}

.delete-batch-btn {
  background-color: #f44336;
  color: white;
}

.delete-batch-btn:hover:not(:disabled) {
  background-color: #d32f2f;
}

.batch-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  opacity: 0.6;
}

/* 策略选择框样式 */
.strategy-checkbox,
.strategy-checkbox-compact {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.strategy-checkbox input[type="checkbox"],
.strategy-checkbox-compact input[type="checkbox"] {
  display: none;
}

.checkmark-small {
  width: 16px;
  height: 16px;
  border: 2px solid #ddd;
  border-radius: 3px;
  position: relative;
  transition: all 0.3s;
}

.strategy-checkbox input[type="checkbox"]:checked + .checkmark-small,
.strategy-checkbox-compact input[type="checkbox"]:checked + .checkmark-small {
  background-color: #0a6e6a;
  border-color: #0a6e6a;
}

.checkmark-small::after {
  content: '';
  position: absolute;
  display: none;
  left: 4px;
  top: 1px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.strategy-checkbox input[type="checkbox"]:checked + .checkmark-small::after,
.strategy-checkbox-compact input[type="checkbox"]:checked + .checkmark-small::after {
  display: block;
}

/* 策略头部布局调整 */
.strategy-header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.strategy-header-right {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

/* 紧凑视图选择框列 */
.checkbox-col {
  width: 60px;
  text-align: center;
}

/* 批量确认对话框样式 */
.batch-confirm-modal {
  background-color: white;
  border-radius: 10px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.strategy-list-preview {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 5px;
  margin-top: 15px;
}

.strategy-preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f5f5f5;
  font-size: 0.9rem;
}

.strategy-preview-item:last-child {
  border-bottom: none;
}

.strategy-name {
  font-weight: 500;
  color: #333;
  flex: 1;
}

.strategy-symbol {
  color: #666;
  margin: 0 10px;
}

.strategy-status {
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 0.8rem;
  font-weight: 500;
}

.confirm-stop-button {
  background-color: #ff9800;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.confirm-stop-button:hover:not(:disabled) {
  background-color: #f57c00;
}

.confirm-stop-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .batch-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .batch-actions {
    justify-content: center;
  }

  .batch-btn {
    flex: 1;
    justify-content: center;
  }

  .batch-confirm-modal {
    width: 95%;
    margin: 20px;
  }

  .strategy-preview-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style>
