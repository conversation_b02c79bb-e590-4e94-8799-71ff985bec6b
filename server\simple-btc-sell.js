/**
 * 简单直接的BTC平仓脚本
 * 直接使用OKX服务卖出所有BTC
 */

const mongoose = require('mongoose');
require('dotenv').config();

async function simpleBTCSell() {
  console.log('🚀 简单BTC平仓');
  console.log('=' .repeat(50));

  try {
    // 连接MongoDB
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/crypto-trading';
    await mongoose.connect(mongoURI, {
      serverSelectionTimeoutMS: 10000
    });
    console.log('✅ 数据库连接成功\n');

    // 1. 直接使用OKX服务获取BTC余额
    console.log('💰 1. 获取BTC余额');
    console.log('-' .repeat(30));
    
    const okxService = require('./services/okxService');
    const userId = 'default';
    const client = okxService.getOkxClient(userId);
    
    if (!client) {
      console.log('❌ 无法创建OKX客户端，API密钥未设置');
      return;
    }

    // 获取账户余额
    const balanceResponse = await client.getBalance();
    console.log('余额响应:', JSON.stringify(balanceResponse, null, 2));

    let btcBalance = null;

    // 处理不同的响应格式
    if (Array.isArray(balanceResponse)) {
      // 直接是数组格式
      for (const balanceData of balanceResponse) {
        if (balanceData.details && Array.isArray(balanceData.details)) {
          btcBalance = balanceData.details.find(detail => detail.ccy === 'BTC');
          if (btcBalance) {
            break;
          }
        }
      }
    } else if (balanceResponse && balanceResponse.data && Array.isArray(balanceResponse.data)) {
      // 包装在data字段中
      for (const balanceData of balanceResponse.data) {
        if (balanceData.details && Array.isArray(balanceData.details)) {
          btcBalance = balanceData.details.find(detail => detail.ccy === 'BTC');
          if (btcBalance) {
            break;
          }
        }
      }
    }
    
    if (!btcBalance) {
      console.log('❌ 未找到BTC余额');
      return;
    }
    
    const availableBTC = parseFloat(btcBalance.availBal);
    console.log(`💰 BTC可用余额: ${availableBTC} BTC`);
    console.log(`💰 BTC总余额: ${btcBalance.eq} BTC`);
    console.log(`💰 BTC冻结: ${btcBalance.frozenBal || 0} BTC`);
    
    if (availableBTC <= 0.00001) {
      console.log('✅ BTC余额太少，无需平仓');
      return;
    }
    
    // 2. 执行BTC卖出
    console.log('\n🚀 2. 执行BTC卖出');
    console.log('-' .repeat(30));
    
    // 计算安全的卖出数量（99%，避免精度问题）
    const sellQuantity = Math.floor(availableBTC * 0.99 * 100000000) / 100000000; // 8位小数
    
    console.log(`计算卖出数量: ${sellQuantity} BTC`);
    console.log(`原始余额: ${availableBTC} BTC`);
    console.log(`安全系数: 99%`);
    
    if (sellQuantity <= 0) {
      console.log('❌ 计算后的卖出数量为0');
      return;
    }
    
    console.log('\n⚠️ 即将执行BTC卖出操作');
    console.log(`卖出数量: ${sellQuantity} BTC`);
    console.log(`交易对: BTC-USDT`);
    console.log(`订单类型: 市价单`);
    
    // 执行市价卖出
    try {
      const sellOrder = await client.submitOrder({
        instId: 'BTC-USDT',
        tdMode: 'cash',
        side: 'sell',
        ordType: 'market',
        sz: String(sellQuantity)
      });
      
      console.log('OKX卖出订单响应:', JSON.stringify(sellOrder, null, 2));
      
      if (sellOrder && sellOrder.data && sellOrder.data[0]) {
        const orderData = sellOrder.data[0];
        
        // 检查订单状态
        if (orderData.sCode === '0') {
          console.log('✅ BTC卖出成功！');
          console.log(`卖出数量: ${sellQuantity} BTC`);
          console.log(`订单ID: ${orderData.ordId}`);
          console.log(`成交价格: ${orderData.px || 'N/A'}`);
          
          // 3. 更新策略记录
          console.log('\n📝 3. 更新策略记录');
          console.log('-' .repeat(30));
          
          const Strategy = require('./models/Strategy');
          const problemStrategyId = '684b74121a5e8072b729761b';
          const strategy = await Strategy.findById(problemStrategyId);
          
          if (strategy) {
            // 移除自动修复的虚假订单
            const autoFixIndex = strategy.orders.findIndex(order => 
              order.orderId && order.orderId.includes('auto-fix')
            );
            
            if (autoFixIndex !== -1) {
              console.log('🗑️ 移除自动修复的虚假订单');
              strategy.orders.splice(autoFixIndex, 1);
            }
            
            // 添加真实的卖出订单
            strategy.orders.push({
              orderId: orderData.ordId,
              side: 'sell',
              type: 'market',
              amount: sellQuantity,
              price: orderData.px || 0,
              status: 'filled',
              timestamp: new Date()
            });
            
            await strategy.save();
            console.log('✅ 策略记录已更新');
            
            // 添加日志
            if (strategy.addLog) {
              await strategy.addLog(`手动修复: 执行真实BTC平仓 ${sellQuantity} BTC，订单ID: ${orderData.ordId}`);
            }
          }
          
          // 4. 验证最终状态
          console.log('\n🔍 4. 验证最终状态');
          console.log('-' .repeat(30));
          
          // 等待一下让订单处理完成
          await new Promise(resolve => setTimeout(resolve, 3000));
          
          const finalBalanceResponse = await client.getBalance();
          let finalBTC = null;

          // 处理不同的响应格式
          if (Array.isArray(finalBalanceResponse)) {
            for (const balanceData of finalBalanceResponse) {
              if (balanceData.details && Array.isArray(balanceData.details)) {
                finalBTC = balanceData.details.find(detail => detail.ccy === 'BTC');
                if (finalBTC) break;
              }
            }
          } else if (finalBalanceResponse && finalBalanceResponse.data && Array.isArray(finalBalanceResponse.data)) {
            for (const balanceData of finalBalanceResponse.data) {
              if (balanceData.details && Array.isArray(balanceData.details)) {
                finalBTC = balanceData.details.find(detail => detail.ccy === 'BTC');
                if (finalBTC) break;
              }
            }
          }

          if (finalBTC) {
            console.log(`最终BTC余额: ${finalBTC.availBal} BTC`);
            if (parseFloat(finalBTC.availBal) <= 0.00001) {
              console.log('✅ 修复完成！BTC已成功平仓');
            } else {
              console.log('⚠️ BTC仍有余额，可能需要再次处理');
            }
          } else {
            console.log('✅ 修复完成！BTC余额已清零');
          }
          
        } else {
          console.log('❌ BTC卖出失败:', `${orderData.sMsg} (${orderData.sCode})`);
        }
      } else {
        console.log('❌ 订单响应格式异常');
      }
      
    } catch (sellError) {
      console.error('❌ 执行BTC卖出失败:', sellError.message);
      
      // 检查是否是余额不足错误
      if (sellError.message.includes('51008') || sellError.message.includes('insufficient')) {
        console.log('💡 可能的原因：');
        console.log('1. 实际可用余额小于计算值');
        console.log('2. 账户中有冻结资金');
        console.log('3. 精度问题导致数量超出限制');
        
        // 尝试更保守的数量
        const conservativeSell = Math.floor(availableBTC * 0.95 * 100000000) / 100000000;
        console.log(`\n🔄 尝试更保守的卖出数量: ${conservativeSell} BTC`);
        
        if (conservativeSell > 0) {
          try {
            const retryOrder = await client.submitOrder({
              instId: 'BTC-USDT',
              tdMode: 'cash',
              side: 'sell',
              ordType: 'market',
              sz: String(conservativeSell)
            });
            
            console.log('重试订单响应:', JSON.stringify(retryOrder, null, 2));
          } catch (retryError) {
            console.error('重试也失败了:', retryError.message);
          }
        }
      }
    }

  } catch (error) {
    console.error('❌ 操作失败:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
  }
}

// 运行简单平仓
if (require.main === module) {
  simpleBTCSell().catch(console.error);
}

module.exports = { simpleBTCSell };
